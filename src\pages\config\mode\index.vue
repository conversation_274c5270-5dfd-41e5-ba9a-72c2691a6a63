<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import {
  BAND_WIDTH_2G,
  CHANNEL_ARR_2G,
  CHANNEL_ARR_5G,
  COUNTRY_OPTIONS,
  ENCRYPTION_TYPE,
  NET_TYPE,
  PROTOCOL_2G,
  PROTOCOL_5G,
  ROAMING_PROTOCOL,
  SPEED_LIMIT_TYPE,
  TX_POWER_2G,
  TX_POWER_5G,
} from '@/utils/constants'

import { isEmptyArray, isNullOrUndefined } from '@core/utils/helpers'
import { requiredValidator } from '@core/utils/validators'
import { isByteLengthInRange } from '@layouts/utils'

const { t } = useI18n()
const router = useRouter()
const selectedRows = ref([])

const itemsPerPage = ref(10)
const page = ref(1)
const dialogStatus = ref(0)

// 正在编辑的模板ID
const editingTemplateId = ref('')

// 动态表头 - 根据模板类型选择
const headers = computed(() => {
  if (filterType.value === '0') {
    // AP模板表头
    return [
      { title: t('Config.Mode.TemplateName'), key: 'name', sortable: false },
      { title: t('Config.Mode.ApplicableModel'), key: 'model', sortable: false },
      { title: t('Config.Mode.Wi-Fi'), key: 'config', sortable: false },
      { title: t('Config.Mode.CreationTime'), key: 'utime', sortable: false },
      { title: t('Config.Mode.DeviceCount'), key: 'deviceNum', sortable: false },
      { title: t('Config.Mode.VLANNum'), key: 'vlanNum', sortable: false },
      { title: t('Config.Mode.Description'), key: 'remark', sortable: false },
      { title: t('Config.Mode.Actions'), key: 'actions', sortable: false },
    ]
  }
  else {
    // AC模板表头 - 不显示WiFi和VLAN数量
    return [
      { title: t('Config.Mode.TemplateName'), key: 'name', sortable: false },
      { title: t('Config.Mode.ApplicableModel'), key: 'model', sortable: false },
      { title: t('Config.Mode.CreationTime'), key: 'utime', sortable: false },
      { title: t('Config.Mode.DeviceCount'), key: 'deviceNum', sortable: false },
      { title: t('Config.Mode.Description'), key: 'remark', sortable: false },
      { title: t('Config.Mode.Actions'), key: 'actions', sortable: false },
    ]
  }
})

// 动态计算表格数据
const templateList = ref<any[]>([])
const totalTemplates = ref(0)

const resetList = () => {
  page.value = 1
  getTemplateList()
}

const getTemplateList = () => {
  templateList.value = []
  totalTemplates.value = 0
  if (filterType.value == 0) {
    $get('v1/apTemplate', { page: page.value, size: itemsPerPage.value }).then((res: any) => {
      if (res.msg === 'success') {
        templateList.value = res.result.rows || []
        totalTemplates.value = res.result.count || 0
      }
      else {
        ElMessage.error(t('Config.Mode.GetTemplatesFailed'))
      }
    })
  }
  if (filterType.value == 1) {
    $get('v1/acTemplate', { page: page.value, size: itemsPerPage.value }).then((res: any) => {
      if (res.msg === 'success') {
        templateList.value = res.result.rows || []
        totalTemplates.value = res.result.count || 0
      }
      else {
        ElMessage.error(t('Config.Mode.GetTemplatesFailed'))
      }
    })
  }
}

watch([page, itemsPerPage], () => {
  getTemplateList()
})

// 新建模版
const createTemplateDialog = ref(false)

// AC模板对话框
const createACTemplateDialog = ref(false)

const deviceDetail = (item: any) => {
  router.push({
    name: 'config-device',
    query: {
      tpl_id: item.id,
      name: item.tpl_name,
    },
  })
}

const editTemplate = (item: any) => {
  if (filterType.value == 0) {
    dialogStatus.value = 1 // 编辑状态
    openDialog()

    // 字段映射，参考 save
    templateForm.tpl_name = item.name || ''
    templateForm.description = item.remark || ''

    // 只赋值 templateForm 里有的字段
    const config = item.config || {}
    for (const key in templateForm) {
      if (Object.prototype.hasOwnProperty.call(item, key) && item[key] !== undefined) {
        if (key === 'model' && item[key] === undefined)
          continue;
        (templateForm as any)[key] = item[key]
      }
      else if (Object.prototype.hasOwnProperty.call(config, key) && config[key] !== undefined) {
        (templateForm as any)[key] = config[key]
      }
    }
    const ssidConfigType = templateForm.ssidConfigType

    // 保留原有的 SSID 计数、带宽等逻辑
    const {
      ssid_type,
      ssid_2g,
      ssid_2g2,
      ssid_5g2,
      wifiAuthmode_2G,
      key_2g,
      wifiApIsolate_2G,
      wifi80211r_2G,
      wifi80211r_5G,
      wifiHtMode_2G,
      wifiForce40MHzMode_2G,
    } = config

    if (ssidConfigType === '1') {
      ssid2gCount.value = ssid_2g2 ? 2 : 1
      ssid5gCount.value = ssid_5g2 ? 2 : 1
    }

    if (ssid_type == '1') {
      templateForm.ssid = ssid_2g
      templateForm.encryptionType = wifiAuthmode_2G
      templateForm.password = key_2g
      templateForm.isolate = wifiApIsolate_2G == '1'
    }
    if (wifi80211r_2G == '1' || wifi80211r_5G == '1') {
      templateForm.roamingProtocol = ROAMING_PROTOCOL[0].value
      templateForm.load_balance_interval = config.load_balance_interval || ''
      templateForm.weak_signal_threshold = config.weak_signal_threshold || ''
      templateForm.ignore_weak_signal = config.ignore_weak_signal || ''
      templateForm.ignore_excessive_retransmission = config.ignore_excessive_retransmission || ''
    }
    else {
      templateForm.roamingProtocol = ''
      templateForm.load_balance_interval = ''
      templateForm.weak_signal_threshold = ''
      templateForm.ignore_weak_signal = ''
      templateForm.ignore_excessive_retransmission = ''
    }

    if (wifiHtMode_2G == 'HT40') {
      if (wifiForce40MHzMode_2G == 0)
        selectedBandwidth.value = '20/40M'
      else
        selectedBandwidth.value = '40M'
    }
    else {
      selectedBandwidth.value = '20M'
    }

    // 设置各个VLAN模板ID
    templateForm.vlanTemplateId = item.vlanId || ''
    templateForm.vlanTemplateId24G1 = item.vlanId24G1 || ''
    templateForm.vlanTemplateId24G2 = item.vlanId24G2 || ''
    templateForm.vlanTemplateId5G1 = item.vlanId5G1 || ''
    templateForm.vlanTemplateId5G2 = item.vlanId5G2 || ''
    templateForm.vlanId = item.config.vlanId || ''
    templateForm.vlanId24G1 = item.config.vlanId24G1 || ''
    templateForm.vlanId24G2 = item.config.vlanId24G2 || ''
    templateForm.vlanId5G1 = item.config.vlanId5G1 || ''
    templateForm.vlanId5G2 = item.config.vlanId5G2 || ''
    templateForm.ap_lan_ip = item.config.ap_lan_ip || ''
    templateForm.ap_lan_mask = item.config.ap_lan_mask || ''
  }
  else {
    // AC模板编辑
    dialogStatus.value = 1 // 编辑状态
    editingTemplateId.value = item.id // 设置编辑的模板ID
    openACDialog(1)

    // 基础信息映射
    acTemplateForm.name = item.name || ''
    acTemplateForm.model = item.model || ''
    acTemplateForm.remark = item.remark || ''

    // 获取配置信息
    const config = item.config || {}

    // 网络配置映射
    if (config.wan) {
      acTemplateForm.networkConfig = {
        networkType: config.wan.networkType || 0,
        mtu: config.wan.mtu || '1500',
        wanUsername: config.wan.wanUsername || '',
        wanPassword: config.wan.wanPassword || '',
        wanIpAddress: config.wan.wanIpAddress || '',
        wanGateway: config.wan.wanGateway || '',
        wanNetmask: config.wan.wanNetmask || '',
        wanDns: config.wan.wanDns || ['', ''],
      }
    }

    // LAN配置映射
    if (config.lan) {
      acTemplateForm.lanConfig = {
        lanIpAddress: config.lan.lanIpAddress || '',
        lanNetmask: config.lan.lanNetmask || '',
        vlanEnabled: config.lan.vlanEnabled || '0',
        vlanTemplateId: config.lan.vlanTemplateId || '',
        dhcpDisabled: config.dhcp.dhcpDisabled || '1',
        dhcpStartValue: config.dhcp.dhcpStartValue || '',
        dhcpMaxNumber: config.dhcp.dhcpMaxNumber || '',
      }
    }

    // 系统配置映射
    if (config.system) {
      acTemplateForm.systemConfig = {
        password: config.system.password || '',
        timeZone: config.system.timeZone || 'Asia/Shanghai',
        hostName: config.system.hostName || '',
      }
    }

    console.log(dialogStatus.value)
  }
}

const createNewTemplate = () => {
  if (actionType.value === '0') {
    dialogStatus.value = 0 // 新建状态
    openDialog()
  }
  else {
    dialogStatus.value = 0 // 新建状态
    openACDialog(0)
  }
}

const openDialog = () => {
  resetForm()
  createTemplateDialog.value = true
  currentTab.value = 0
}

// AC模板相关函数
const openACDialog = num => {
  if (num == 0)
    resetACForm()

  createACTemplateDialog.value = true
  currentACTab.value = 0
}

const resetForm = () => {
  // 重置SSID计数
  ssid2gCount.value = 1
  ssid5gCount.value = 1

  templateForm = reactive({
    id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
    tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
    model: modelList.value.length > 0 ? String(modelList.value[0].value) : '', // AP型号，默认选中第一个
    tpl_bands: '', // 适用频段
    ssid_count: '', // SSID数量
    modified_at: '', //	最后修改时间
    description: '', //	备注，支持中文（UTF-8）一个中文占三字节
    ssid_2g: '', //	2G SSID
    ssid_5g: '', //	5G SSID
    key_2g: '', //	2G密码
    key_5g: '', //	5G密码
    ssid_type: '0', //	1：SSID双频合一 0：SSID分开
    ssidConfigType: '0', // 0：默认配置 1：多SSID配置
    net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
    ap_lan_ip: '', //	LAN IP
    ap_lan_mask: '', //	LAN子网掩码
    wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
    wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
    wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
    wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
    wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
    wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
    wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
    wifiCountry_5G: 'CN', //	5.8G 国家代码
    wifiChannel_2G: 'auto', //	2.4G 信道
    wifiChannel_5G: 'auto', //	5.8G 信道
    wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
    wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
    wifiHtMode_2G: 'HT20', //	2.4G带宽
    wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
    wifiHtMode_5G: 'HT160', //	5.8G带宽
    wifiTxpower_2G: '', // 2.4G信号调节
    wifiTxpower_5G: '', // 5.8G信号调节
    wifi80211r_2G: '', //	2.4G快速漫游r协议
    wifi80211r_5G: '', //	5.8G快速漫游r协议
    wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
    wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
    wifiWpa3_2G: '', //	2.4G wpa3 开启标志
    wifiWpa3_5G: '', //	5.8G wpa3 开启标志

    // 多SSID配置字段
    ssid_2g2: '', // 2.4G SSID 2
    key_2g2: '', // 2.4G 密码 2
    vlanId24G1: '', // 2.4G SSID 1 VLAN ID
    vlanId24G2: '', // 2.4G SSID 2 VLAN ID
    vlanTemplateId24G1: '', // 2.4G SSID 1 VLAN模板ID
    vlanTemplateId24G2: '', // 2.4G SSID 2 VLAN模板ID
    wifiMaxsta_2G2: '', // 2.4G SSID 2 最大连接数
    wifiApIsolate_2G2: '0', // 2.4G SSID 2 AP隔离
    ssid_5g2: '', // 5G SSID 2
    key_5g2: '', // 5G 密码 2
    vlanId5G1: '', // 5G SSID 1 VLAN ID
    vlanId5G2: '', // 5G SSID 2 VLAN ID
    vlanTemplateId5G1: '', // 5G SSID 1 VLAN模板ID
    vlanTemplateId5G2: '', // 5G SSID 2 VLAN模板ID
    wifiMaxsta_5G2: '', // 5G SSID 2 最大连接数
    wifiApIsolate_5G2: '0', // 5G SSID 2 AP隔离
    // 双频合一的参数
    ssid: '', // SSID名称
    encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
    password: '', // 密码
    isolate: false, // 隔离
    // 漫游配置
    quickRoaming: false, // 快速漫游
    roamingProtocol: undefined, // 漫游协议
    // 以下是暂不使用的变量
    networkLimit: undefined, // 限速
    upstreamLimit: '', // 上行限制
    downstreamLimit: '', // 下行限制
    wifiMaxsta_2G: '',
    wifiMaxsta_5G: '',
    vlanNum: 0, // VLAN模板数量
    vlanId: '', // VLAN ID
    vlanTemplateId: '', // VLAN模板ID
    load_balance_interval: '', // SSID负载均衡间隔
    weak_signal_threshold: '', // 信号阙值
    ignore_weak_signal: '', // 忽略弱信号STA
    ignore_excessive_retransmission: '', // 忽略重传过多STA
  })
}

// AC模板重置函数
const resetACForm = () => {
  dialogStatus.value = 0 // 重置为新建状态
  editingTemplateId.value = '' // 清空编辑ID
  acTemplateForm.name = ''
  acTemplateForm.model = acModelList.value && acModelList.value.length > 0 ? String(acModelList.value[0].value) : ''
  acTemplateForm.remark = ''
  acTemplateForm.networkConfig = {
    networkType: 0, // 默认DHCP
    mtu: '1500', // 默认MTU值
    wanUsername: '',
    wanPassword: '',
    wanIpAddress: '',
    wanGateway: '',
    wanNetmask: '',
    wanDns: ['', ''],
  }
  acTemplateForm.lanConfig = {
    lanIpAddress: '',
    lanNetmask: '',
    vlanEnabled: '0',
    vlanTemplateId: '',
    dhcpDisabled: '1',
    dhcpStartValue: '',
    dhcpMaxNumber: '',
  }
  acTemplateForm.systemConfig = {
    password: '',
    timeZone: 'Asia/Shanghai', // 默认时区，参考系统配置
    hostName: '',
  }
}

const nextStepAC = () => {
  acFormRef[currentACTab.value].value?.validate().then(res => {
    if (res.valid)
      currentACTab.value += 1
  })
}

// AC模板保存函数
const saveACTemplate = async () => {
  // 验证所有表单
  for (let i = 0; i < acFormRef.length; i++) {
    const validator = acFormRef[i]
    const valid = await validator.value?.validate()

    if (!valid?.valid) {
      currentACTab.value = i

      return false
    }
  }
  const { lanConfig } = acTemplateForm

  const postData = {
    name: acTemplateForm.name,
    model: acTemplateForm.model,
    remark: acTemplateForm.remark,
    vlanId: lanConfig.vlanTemplateId,
    config: {
      wan: acTemplateForm.networkConfig,
      lan: {
        lanIpAddress: lanConfig.lanIpAddress, // LAN IP地址
        lanNetmask: lanConfig.lanNetmask, // LAN子网掩码
        vlanEnabled: lanConfig.vlanEnabled, // VLAN管理：0-禁用，1-启用
        vlanTemplateId: lanConfig.vlanTemplateId, // VLAN模板ID
      },
      dhcp: {
        dhcpDisabled: lanConfig.dhcpDisabled, // DHCP服务：0-启用，1-禁用
        dhcpStartValue: lanConfig.dhcpStartValue, // DHCP起始值
        dhcpMaxNumber: lanConfig.dhcpMaxNumber, // DHCP最大数量
      },
      system: acTemplateForm.systemConfig,
    },
  }

  try {
    let res
    console.log(dialogStatus.value)
    if (dialogStatus.value === 1) {
      // 编辑模式 - 需要传递模板ID
      res = await $put(`/v1/acTemplate/${editingTemplateId.value}`, postData)
    }
    else {
      // 新建模式
      res = await $post('/v1/acTemplate', postData)
    }

    if (res.msg === 'success') {
      ElMessage.success(dialogStatus.value === 1 ? t('Config.Mode.UpdateSuccess') : t('Config.Mode.SaveSuccess'))
      createACTemplateDialog.value = false
      resetACForm()
      resetList()
    }
    else {
      ElMessage.error(dialogStatus.value === 1 ? t('Config.Mode.UpdateFailed') : t('Config.Mode.SaveFailed'))
    }
  }
  catch (error) {
    ElMessage.error(dialogStatus.value === 1 ? t('Config.Mode.UpdateFailed') : t('Config.Mode.SaveFailed'))
  }
}

const copyTemplate = (item: any) => {
  const new_temp = JSON.parse(JSON.stringify(item))

  if (filterType.value == '0') {
    $post(`/v1/apTemplateClone/${item.id}`, {
      name: `${new_temp.name}-copy`,
    }).then((res: any) => {
      if (res.msg === 'success')
        resetList()

      else
        ElMessage.error(res.msg)
    })
  }
  else {
    $post(`/v1/acTemplateClone/${item.id}`, {
      name: `${new_temp.name}-copy`,
    }).then((res: any) => {
      if (res.msg === 'success')
        resetList()
      else
        ElMessage.error(res.msg)
    })
  }

  // resetList()
}

const deleteTemplate = (item: any) => {
  if (filterType.value == 0) {
    if (item.id) {
      ElMessageBox.confirm(
        t('Config.Mode.DeleteConfirm', { name: item.name }),
        t('Config.Mode.Tips'),
        {
          confirmButtonText: t('Config.Mode.Confirm'),
          cancelButtonText: t('Config.Mode.Cancel'),
          type: 'warning',
        },
      ).then(() => {
        $delete(`/v1/apTemplate/${item.id}`, {}).then((res: any) => {
          if (res.msg === 'success') {
            ElMessage.success(t('Config.Mode.DeleteSuccess'))
            resetList()
          }
          else {
            ElMessage.error(res.msg)
          }
        })
      })
    }
  }
  else {
    // AC模板删除
    if (item.id) {
      ElMessageBox.confirm(
        t('Config.Mode.DeleteConfirm', { name: item.name }),
        t('Config.Mode.Tips'),
        {
          confirmButtonText: t('Config.Mode.Confirm'),
          cancelButtonText: t('Config.Mode.Cancel'),
          type: 'warning',
        },
      ).then(() => {
        $delete(`/v1/acTemplate/${item.id}`, {}).then((res: any) => {
          if (res.msg === 'success') {
            ElMessage.success(t('Config.Mode.DeleteSuccess'))
            resetList()
          }
          else {
            ElMessage.error(res.msg)
          }
        })
      })
    }
  }
}

const currentTab = ref(0)

const tabList = ref([
  { label: t('Config.Mode.BasicSettings'), value: 0 },
  { label: t('Config.Mode.SSIDConfig'), value: 1 },
  { label: t('Config.Mode.AdvancedConfig'), value: 2 },
  { label: t('Config.Mode.RoamingConfig'), value: 3 },
])

// AC模板相关变量
const currentACTab = ref(0)

const acTabList = ref([
  { label: t('Config.Mode.BasicSettings'), value: 0 },
  { label: t('Config.AP.NetworkSettings'), value: 1 },
  { label: t('Config.AP.LANSettings'), value: 2 },
  { label: t('Config.AP.SystemConfig'), value: 3 },
])

const formRef1 = ref()
const formRef2 = ref()
const formRef3 = ref()
const formRef4 = ref()
const formRef = [formRef1, formRef2, formRef3, formRef4]

// AC模板表单引用
const acFormRef1 = ref()
const acFormRef2 = ref()
const acFormRef3 = ref()
const acFormRef4 = ref()
const acFormRef = [acFormRef1, acFormRef2, acFormRef3, acFormRef4]

// 使用从constants.ts直接导入的ROAMING_PROTOCOL

const showMergePassword = ref(false)
const show2GPassword = ref(false)
const show5GPassword = ref(false)

// AC模板表单数据
const acTemplateForm = reactive({
  name: '', // 模板名称
  model: '', // AC型号
  remark: '', // 备注
  // 网络设置
  networkConfig: {
    networkType: 0, // 网络类型：0-DHCP, 1-PPPoE, 2-静态IP
    mtu: '1500', // MTU值，所有模式都可填
    // PPPoE 模式字段
    wanUsername: '', // PPPoE用户名
    wanPassword: '', // PPPoE密码
    // 静态IP 模式字段
    wanIpAddress: '', // IP地址
    wanGateway: '', // 网关
    wanNetmask: '', // 子网掩码
    wanDns: ['', ''], // DNS服务器数组，两个组
  },

  // LAN设置
  lanConfig: {
    lanIpAddress: '', // LAN IP地址
    lanNetmask: '', // LAN子网掩码
    vlanEnabled: '0', // VLAN管理：0-禁用，1-启用
    vlanTemplateId: '', // VLAN模板ID
    dhcpDisabled: '1', // DHCP服务：0-启用，1-禁用
    dhcpStartValue: '', // DHCP起始值
    dhcpMaxNumber: '', // DHCP最大数量
  },

  // 系统配置
  systemConfig: {
    password: '', // 管理员密码
    timeZone: 'Asia/Shanghai', // 时区，默认值参考系统配置
    hostName: '', // 设备名称
  },
})

let templateForm = reactive({
  id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
  tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
  model: '', // AP型号，默认选中第一个
  tpl_bands: '', // 适用频段
  ssid_count: '', // SSID数量
  modified_at: '', //	最后修改时间
  description: '', //	备注，支持中文（UTF-8）一个中文占三字节
  ssid_2g: '', //	2G SSID
  ssid_5g: '', //	5G SSID
  key_2g: '', //	2G密码
  key_5g: '', //	5G密码
  ssid_type: '0', //	1：SSID双频合一 0：SSID分开
  ssidConfigType: '0', // 0：默认配置 1：多SSID配置
  net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
  ap_lan_ip: '', //	LAN IP
  ap_lan_mask: '', //	LAN子网掩码
  wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
  wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
  wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
  wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
  wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
  wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
  wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
  wifiCountry_5G: 'CN', //	5.8G 国家代码
  wifiChannel_2G: 'auto', //	2.4G 信道
  wifiChannel_5G: 'auto', //	5.8G 信道
  wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
  wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
  wifiHtMode_2G: 'HT20', //	2.4G带宽
  wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
  wifiHtMode_5G: 'HT160', //	5.8G带宽
  wifiTxpower_2G: '', // 2.4G信号调节
  wifiTxpower_5G: '', // 5.8G信号调节
  wifi80211r_2G: '', //	2.4G快速漫游r协议
  wifi80211r_5G: '', //	5.8G快速漫游r协议
  wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
  wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
  wifiWpa3_2G: '', //	2.4G wpa3 开启标志
  wifiWpa3_5G: '', //	5.8G wpa3 开启标志
  // 多SSID配置字段
  ssid_2g2: '', // 2.4G SSID 2
  key_2g2: '', // 2.4G 密码 2
  vlanId24G1: '', // 2.4G SSID 1 VLAN ID
  vlanId24G2: '', // 2.4G SSID 2 VLAN ID
  vlanTemplateId24G1: '', // 2.4G SSID 1 VLAN模板ID
  vlanTemplateId24G2: '', // 2.4G SSID 2 VLAN模板ID
  wifiMaxsta_2G2: '', // 2.4G SSID 2 最大连接数
  wifiApIsolate_2G2: '0', // 2.4G SSID 2 AP隔离
  ssid_5g2: '', // 5G SSID 2
  key_5g2: '', // 5G 密码 2
  vlanId5G1: '', // 5G SSID 1 VLAN ID
  vlanId5G2: '', // 5G SSID 2 VLAN ID
  vlanTemplateId5G1: '', // 5G SSID 1 VLAN模板ID
  vlanTemplateId5G2: '', // 5G SSID 2 VLAN模板ID
  wifiMaxsta_5G2: '', // 5G SSID 2 最大连接数
  wifiApIsolate_5G2: '0', // 5G SSID 2 AP隔离
  // 双频合一的参数
  ssid: '', // SSID名称
  encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
  password: '', // 密码
  isolate: false, // 隔离
  // 漫游配置
  quickRoaming: false, // 快速漫游
  roamingProtocol: undefined, // 漫游协议
  // 以下是暂不使用的变量
  networkLimit: undefined, // 限速
  upstreamLimit: '', // 上行限制
  downstreamLimit: '', // 下行限制
  wifiMaxsta_2G: '',
  wifiMaxsta_5G: '',
  vlanNum: 0, // VLAN模板数量
  // VLAN配置字段
  vlanId: '', // VLAN ID (1-4094)
  vlanTemplateId: '', // VLAN模板ID
  load_balance_interval: '', // SSID负载均衡间隔
  weak_signal_threshold: '', // 信号阙值
  ignore_weak_signal: '', // 忽略弱信号STA
  ignore_excessive_retransmission: '', // 忽略重传过多STA
})

const channelOptions2g = computed(() => {
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === templateForm.wifiCountry_2G)

  return CHANNEL_ARR_2G[index] || []
})

const channelOptions5g = computed(() => {
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === templateForm.wifiCountry_5G)

  return CHANNEL_ARR_5G[index] || []
})

// 直接监听channelOptions的变化
watch(templateForm.wifiCountry_2G, () => {
  templateForm.wifiChannel_2G = 'auto'
}, { immediate: true })

watch(templateForm.wifiCountry_5G, () => {
  templateForm.wifiChannel_5G = 'auto'
}, { immediate: true })

const nextStep = () => {
  formRef[currentTab.value].value?.validate().then(res => {
    if (res.valid)
      currentTab.value += 1
  })
}

const validAllForm = async () => {
  for (let i = 0; i < formRef.length; i++) {
    const validator = formRef[i]
    const valid = await validator.value?.validate()

    if (!valid?.valid) {
      currentTab.value = i

      return false
    }
  }

  return true
}

const ssidTypeChange = () => {
  formRef2.value?.resetValidation()
}

const ssidConfigTypeChange = () => {
  formRef2.value?.resetValidation()

  // 切换到多SSID配置时，重置双频合一为分开模式
  if (templateForm.ssidConfigType === '1')
    templateForm.ssid_type = '0'
}

const save = async () => {
  if (!(await validAllForm()))
    return

  // 先执行VLAN计算
  calculateVlanNum()

  // 等待VLAN计算完成后再进行数据提取和提交
  await nextTick()

  let {
    id,
    tpl_name,
    tpl_bands,
    model,
    ssid_count,
    description,
    ssid_2g,
    ssid_5g,
    key_2g,
    key_5g,
    ssid_type,
    ssidConfigType,
    net_type,
    ap_lan_ip,
    ap_lan_mask,
    wifiOnOff_2G,
    wifiOnOff_5G,
    wifiApIsolate_2G,
    wifiApIsolate_5G,
    wifiEnable_2G,
    wifiEnable_5G,
    wifiCountry_2G,
    wifiCountry_5G,
    wifiChannel_2G,
    wifiChannel_5G,
    wifiHwMode_2G,
    wifiHwMode_5G,
    wifiHtMode_2G,
    wifiForce40MHzMode_2G,
    wifiHtMode_5G,
    wifiTxpower_2G,
    wifiTxpower_5G,
    wifi80211r_2G,
    wifi80211r_5G,
    wifiAuthmode_2G,
    wifiAuthmode_5G,
    wifiWpa3_2G,
    wifiWpa3_5G,
    ssid,
    encryptionType,
    password,
    isolate,
    quickRoaming,
    roamingProtocol,
    wifiMaxsta_2G,
    wifiMaxsta_5G,

    // 多SSID配置字段
    ssid_2g2,
    key_2g2,

    wifiMaxsta_2G2,
    wifiApIsolate_2G2,
    ssid_5g2,
    key_5g2,

    wifiMaxsta_5G2,
    wifiApIsolate_5G2,
    vlanNum,
    vlanId,
    vlanId24G1,
    vlanId24G2,
    vlanId5G1,
    vlanId5G2,
    vlanTemplateId,
    vlanTemplateId24G1,
    vlanTemplateId24G2,
    vlanTemplateId5G1,
    vlanTemplateId5G2,
    load_balance_interval,
    weak_signal_threshold,
    ignore_weak_signal,
    ignore_excessive_retransmission,
  } = templateForm

  const vlan = vlanList.value.find(v => v.id === vlanTemplateId)
  const vlan24G1 = vlanList.value.find(v => v.id === vlanTemplateId24G1)
  const vlan24G2 = vlanList.value.find(v => v.id === vlanTemplateId24G2)
  const vlan5G1 = vlanList.value.find(v => v.id === vlanTemplateId5G1)
  const vlan5G2 = vlanList.value.find(v => v.id === vlanTemplateId5G2)

  vlanId = vlan?.vlanId || ''
  vlanId24G1 = vlan24G1?.vlanId || ''
  vlanId24G2 = vlan24G2?.vlanId || ''
  vlanId5G1 = vlan5G1?.vlanId || ''
  vlanId5G2 = vlan5G2?.vlanId || ''

  // 处理默认配置的双频合一
  if (ssidConfigType === '0' && ssid_type == '1') {
    ssid_2g = ssid
    ssid_5g = ssid
    key_2g = password
    key_5g = password
    wifiApIsolate_2G = isolate ? '1' : '0'
    wifiApIsolate_5G = isolate ? '1' : '0'
    wifiAuthmode_2G = encryptionType
    wifiAuthmode_5G = encryptionType
    tpl_bands = '2.4G/5G'
  }
  else {
    // 计算频段信息
    const bands = []
    if (wifiOnOff_2G == '0')
      bands.push('2.4G')
    if (wifiOnOff_5G == '0')
      bands.push('5G')
    tpl_bands = bands.length > 0 ? bands.join('/') : '-'
  }

  // 处理多SSID配置的SSID数量
  if (ssidConfigType === '1') {
    let count = 0
    if (wifiOnOff_2G == '0')
      count += ssid2gCount.value
    if (wifiOnOff_5G == '0')
      count += ssid5gCount.value
    ssid_count = count.toString()
  }
  else {
    ssid_count = '1'
  }

  if (wifiAuthmode_2G === 'psk2')
    wifiWpa3_2G = '1'
  else
    wifiWpa3_2G = '0'

  if (wifiAuthmode_5G === 'psk2')
    wifiWpa3_5G = '1'
  else
    wifiWpa3_5G = '0'

  if (wifiAuthmode_2G == 'none')
    key_2g = ''

  if (wifiAuthmode_5G == 'none')
    key_5g = ''

  let postData = {}
  if (ssidConfigType === '1') {
    if (dialogStatus == 1) {
      postData = {
        id,
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: '', // 2.4G1 VLAN模板ID
        vlanId24G2: '', // 2.4G2 VLAN模板ID
        vlanId5G1: '', // 5G1 VLAN模板ID
        vlanId5G2: '', // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId: '',
          vlanId24G1: '',
          vlanId24G2: '',
          vlanId5G1: '',
          vlanId5G2: '',
          ssid_2g2: '',
          key_2g2: '',
          wifiMaxsta_2G2: '',
          wifiApIsolate_2G2: '',
          ssid_5g2: '',
          key_5g2: '',
          wifiMaxsta_5G2: '',
          wifiApIsolate_5G2: '',
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
    else {
      postData = {
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId: vlanId || '',
          vlanId24G1: '',
          vlanId24G2: '',
          vlanId5G1: '',
          vlanId5G2: '',
          ssid_2g2: '',
          key_2g2: '',
          wifiMaxsta_2G2: '',
          wifiApIsolate_2G2: '',
          ssid_5g2: '',
          key_5g2: '',
          wifiMaxsta_5G2: '',
          wifiApIsolate_5G2: '',
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
  }
  else {
    if (dialogStatus == 1) {
      postData = {
        id,
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId,
          vlanId24G1,
          vlanId24G2,
          vlanId5G1,
          vlanId5G2,
          ssid_2g2,
          key_2g2,
          wifiMaxsta_2G2,
          wifiApIsolate_2G2,
          ssid_5g2,
          key_5g2,
          wifiMaxsta_5G2,
          wifiApIsolate_5G2,
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
    else {
      postData = {
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId,
          vlanId24G1,
          vlanId24G2,
          vlanId5G1,
          vlanId5G2,
          ssid_2g2,
          key_2g2,
          wifiMaxsta_2G2,
          wifiApIsolate_2G2,
          ssid_5g2,
          key_5g2,
          wifiMaxsta_5G2,
          wifiApIsolate_5G2,
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
  }
  console.log(postData)
  if (id) {
  // 使用新的postData结构进行提交
    $put(`/v1/apTemplate/${id}`, postData).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        createTemplateDialog.value = false
        resetForm()
        resetList()
      }
      else {
        ElMessage.error(t('Config.Mode.SaveFailed'))
      }
    })
  }
  else {
  // 使用新的postData结构进行提交
    $post('/v1/apTemplate', postData).then(res => {
      if (res.msg === 'success') {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        createTemplateDialog.value = false
        resetForm()
        resetList()
      }
      else {
        ElMessage.error(t('Config.Mode.SaveFailed'))
      }
    })
  }
}

const modelList = ref()

const getApList = () => {
  $get('/v1/apModel', { }).then(res => {
    if (res.msg === 'success') {
      // 添加调试日志
      console.log('API响应:', res)

      const models = res.result || []

      console.log('提取的型号:', Array.from(models))

      // 先加上"请选择"选项
      modelList.value = [
        ...Array.from(models).map(item => ({
          label: String(item),
          value: String(item),
        })),
      ]

      console.log('modelList:', modelList.value)
    }
  }).catch(err => {
    console.error('获取AP列表失败:', err)
  })
}

// 获取AC型号列表
const acModelList = ref()

const getAcList = () => {
  $get('/v1/acModel', { }).then(res => {
    if (res.msg === 'success') {
      console.log('AC API响应:', res)

      const models = res.result || []

      console.log('提取的AC型号:', Array.from(models))

      acModelList.value = [
        ...Array.from(models).map(item => ({
          label: String(item),
          value: String(item),
        })),
      ]

      console.log('acModelList:', acModelList.value)
    }
  }).catch(err => {
    console.error('获取AC列表失败:', err)
  })
}

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

// ========== 校验函数 - 参考 /system/networkConfig 页面 ==========

// IPv4地址验证器
const isValidIPv4 = (ip: string): boolean => {
  const ipv4Regex = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/

  return ipv4Regex.test(ip)
}

// PPPoE账号密码验证器 - 参考系统网络配置
const validateAccountAndPassword = (value: string) => {
  const reg = /^[\w~!@#$%^&*()+`\-=[\]{}<>;:|?,./]*$/

  return reg.test(value)
}

// LAN IP地址验证器 - 参考系统LAN配置
const lanIpValidator = (value: any) => {
  return !!value || t('NetworkConfig.LAN.Required')
}

// 子网掩码验证器 - 参考系统LAN配置
const subnetMaskValidator = (value: any) => {
  return !!value || t('NetworkConfig.LAN.Required')
}

const calculateHostBits = (subnetMask: string): number => {
  // 将子网掩码转换为二进制字符串
  const binarySubnetMask = subnetMask
    .split('.')
    .map((octet: string) => Number.parseInt(octet).toString(2).padStart(8, '0'))
    .join('')

  // 计算主机位数
  return binarySubnetMask.length - binarySubnetMask.indexOf('0')
}

const maxAddressesLimit = computed(() => {
  // 计算子网掩码中的主机位数
  const hostBits = calculateHostBits(acTemplateForm.lanConfig.lanNetmask)

  return 2 ** hostBits - 2
})

const maxAddresses = computed(() => {
  // 计算子网掩码中的主机位数
  const hostBits = calculateHostBits(acTemplateForm.lanConfig.lanNetmask)

  // 计算最大数量的限额  // 计算最大可用主机数
  const maxHosts = 2 ** hostBits - 2

  console.log('最大可用主机数:', maxHosts)

  let maxNum = maxHosts - (acTemplateForm.lanConfig.dhcpStartValue - 1)
  if (maxNum < 1)
    maxNum = 1

  return maxNum
})

// DHCP起始值验证器 - 参考系统LAN配置，增强版本
const dhcpValidator = (value: any) => {
  const numValue = Number(value)

  // 如果DHCP禁用，直接返回true
  if (acTemplateForm.lanConfig.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 数值有效性和最大值校验
  if (!Number.isNaN(numValue) && numValue >= maxAddressesLimit.value)
    return `${t('NetworkConfig.LAN.MaxValueExceeded')}${maxAddressesLimit.value}`

  return true
}

// DHCP最大数量验证器 - 参考系统LAN配置，增强版本
const dhcpMaxValidator = (value: any) => {
  // 将value转换为数字进行比较
  const numValue = Number(value)

  console.log(numValue, maxAddresses.value)

  // 如果DHCP禁用，直接返回true
  if (acTemplateForm.lanConfig.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 数值有效性和最大值校验
  if (!Number.isNaN(numValue) && numValue >= maxAddresses.value)
    return `${t('NetworkConfig.LAN.MaxValueExceededA')}${maxAddresses.value - 1}${t('NetworkConfig.LAN.MaxValueExceededB')}`

  return true
}

// VLAN配置验证器 - 仅在VLAN管理启用时校验
const vlanValidator = (value: any) => {
  // 如果VLAN管理禁用，直接返回true
  if (acTemplateForm.lanConfig.vlanEnabled === '0')
    return true

  // 空值校验
  if (!value)
    return t('Config.Mode.SelectVLAN')

  return true
}

// 防止小数点输入 - 参考系统LAN配置
const preventDecimal = (event: KeyboardEvent) => {
  if (event.key === '.' || event.key === ',')
    event.preventDefault()
}

// 处理整数输入 - 参考系统LAN配置
const handleIntegerInput = (event: Event, field: string) => {
  const target = event.target as HTMLInputElement
  const value = target.value

  // 移除非数字字符
  const numericValue = value.replace(/\D/g, '')

  // 更新对应字段
  if (field === 'dhcpStartValue')
    acTemplateForm.lanConfig.dhcpStartValue = numericValue
  else if (field === 'dhcpMaxNumber')
    acTemplateForm.lanConfig.dhcpMaxNumber = numericValue
}

// 当前时间显示
const currentTime = ref('')
const currentTimezone = ref('')

// AC模板管理员密码显示控制 - 参考AP模板密码实现
const showAdminPassword = ref(false)

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  const timezone = acTemplateForm.systemConfig.timeZone || 'Asia/Shanghai'

  // 格式化时间为 2025/5/14 11:49:52
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')

  currentTime.value = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`

  // 获取时区信息
  const timezoneInfo = getTimezoneInfo(timezone)

  currentTimezone.value = `(${timezone} ${timezoneInfo})`
}

// 获取时区信息
const getTimezoneInfo = (timezone: string) => {
  const now = new Date()

  const formatter = new Intl.DateTimeFormat('en', {
    timeZone: timezone,
    timeZoneName: 'short',
  })

  try {
    const parts = formatter.formatToParts(now)
    const timeZoneName = parts.find(part => part.type === 'timeZoneName')?.value || ''

    // 获取UTC偏移
    const offset = getTimezoneOffset(timezone)

    return `${timeZoneName}`
  }
  catch (error) {
    return 'GMT+8'
  }
}

// 获取时区偏移
const getTimezoneOffset = (timezone: string) => {
  try {
    const now = new Date()
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000))
    const targetTime = new Date(utc.toLocaleString('en-US', { timeZone: timezone }))
    const offset = (targetTime.getTime() - utc.getTime()) / (1000 * 60 * 60)

    const sign = offset >= 0 ? '+' : '-'
    const absOffset = Math.abs(offset)
    const hours = Math.floor(absOffset)
    const minutes = Math.round((absOffset - hours) * 60)

    if (minutes === 0)
      return `GMT${sign}${hours}`
    else
      return `GMT${sign}${hours}:${minutes.toString().padStart(2, '0')}`
  }
  catch (error) {
    return 'GMT+8'
  }
}

// 时区列表
const timezoneList = ref([])

// 获取时区列表 - 完全参考 /system/systemConfig 的实现
const getTimezoneList = () => {
  $get('/v1/timezone', {}).then(res => {
    console.log(res)
    if (res.msg == 'success') {
      const zoneNameArray = JSON.parse(res.result)

      timezoneList.value = zoneNameArray.map((item: any) => {
        return {
          label: item,
          value: item,
        }
      })
    }
  }).catch(err => {
    console.error('获取时区列表失败:', err)
  })
}

// 时间更新定时器
let timeInterval: NodeJS.Timeout | null = null

onMounted(async () => {
  getApList()
  getAcList()
  getTemplateList()
  getVlanList()
  getTimezoneList()

  // 启动时间更新
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  // 清理定时器
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
})

// 监听时区变化，更新时间显示
watch(() => acTemplateForm.systemConfig.timeZone, () => {
  updateCurrentTime()
})

const vlanList = ref<any[]>([])

const getVlanList = () => {
  $get('/v1/vlanConfigs', { page: 1, size: 1000 }).then((res: any) => {
    if (res.msg === 'success' || res.msg === 'success') {
      // 兼容不同后端返回结构
      const rows = res.result?.rows || []

      vlanList.value = [
        { id: '', vlanId: '' },
        ...rows,
      ]

      vlanList.value.forEach(item => {
        if (item.id)
          item.itemTitle = `${item.name} VLAN ${item.vlanId} ${item.vlanId}`

        else
          item.itemTitle = t('PleaseSelect')
      })
    }
    else {
      ElMessage.error(res.err_message || res.msg || '获取VLAN列表失败')
    }
  })
}

// 计算VLAN模板数量
const calculateVlanNum = () => {
  const vlanIds = new Set()

  // 收集所有使用的VLAN模板ID，空值不参与计算
  if (templateForm.vlanTemplateId24G1 && templateForm.vlanTemplateId24G1 !== '')
    vlanIds.add(templateForm.vlanTemplateId24G1)
  if (templateForm.vlanTemplateId24G2 && templateForm.vlanTemplateId24G2 !== '')
    vlanIds.add(templateForm.vlanTemplateId24G2)
  if (templateForm.vlanTemplateId5G1 && templateForm.vlanTemplateId5G1 !== '')
    vlanIds.add(templateForm.vlanTemplateId5G1)
  if (templateForm.vlanTemplateId5G2 && templateForm.vlanTemplateId5G2 !== '')
    vlanIds.add(templateForm.vlanTemplateId5G2)
  if (templateForm.vlanTemplateId && templateForm.vlanTemplateId !== '')
    vlanIds.add(templateForm.vlanTemplateId)

  console.log(vlanIds, templateForm)
  templateForm.vlanNum = vlanIds.size
}

// VLAN选择处理函数
const handleVlanSelect = () => {
}

// 对constants中的国际化常量进行本地化处理
const COUNTRY_OPTIONS_LOCALIZED = computed(() => {
  return COUNTRY_OPTIONS.map(item => ({
    ...item,
    label: item.label, // 国家代码不需要翻译
  }))
})

const ENCRYPTION_TYPE_LOCALIZED = computed(() => {
  return ENCRYPTION_TYPE.map(item => ({
    ...item,
    label: item.label === 'None' ? t('Config.Mode.None') : item.label, // 只有None需要翻译，其他是技术标准
  }))
})

const BAND_WIDTH_2G_LOCALIZED = computed(() => {
  return BAND_WIDTH_2G.map(item => ({
    ...item,
    label: item.label, // 带宽值不需要翻译
  }))
})

const PROTOCOL_2G_LOCALIZED = computed(() => {
  return PROTOCOL_2G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

const PROTOCOL_5G_LOCALIZED = computed(() => {
  return PROTOCOL_5G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => {
    if (item.label.startsWith('txPower.')) {
      return {
        ...item,
        label: t(item.label), // 发射功率需要翻译
      }
    }

    return item
  })
})

const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => {
    if (item.label.startsWith('txPower.')) {
      return {
        ...item,
        label: t(item.label), // 发射功率需要翻译
      }
    }

    return item
  })
})

const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => {
    if (item.label.startsWith('NetworkConfig.Modes.')) {
      return {
        ...item,
        label: t(item.label), // 网络类型需要翻译
      }
    }

    return item
  })
})

// 对SPEED_LIMIT_TYPE进行本地化处理
const SPEED_LIMIT_TYPE_LIST = computed(() => {
  return SPEED_LIMIT_TYPE.map(item => ({
    ...item,
    label: t(item.label), // 国家代码不需要翻译
  }))
})

const selectedBandwidth = ref('20M')

watch(selectedBandwidth, newVal => {
  switch (newVal) {
    case '40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '1'
      break
    case '20/40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
    case '20M':
    default:
      templateForm.wifiHtMode_2G = 'HT20'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
  }
}, { immediate: true })

const validatePassword = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.encryptionType === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEightMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5G = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5GMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 新增：根据5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = templateForm.wifiChannel_5G

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = templateForm.wifiHtMode_5G
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    templateForm.wifiHtMode_5G = newBandWidthOptions[0].value
  }
}

const requiredValidatorNew = (value: unknown, message: string) => {
  // 默认配置且双频合一时不需要验证分开的SSID
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'This field is required'

  return !!String(value).trim().length || message || 'This field is required'
}

const filterType = ref('0')

const filterTypeList = ref([
  {
    label: 'AP',
    value: '0',
  },
  {
    label: 'AC',
    value: '1',
  },
])

// 监听 AC/AP 切换，重新请求数据
watch(filterType, () => {
  resetList()
})

const actionTypeList = computed(() => [
  { label: t('Config.Mode.NewAPTemplate'), value: '0' },
  { label: t('Config.Mode.NewACTemplate'), value: '1' },
])

const actionType = ref('0')

// SSID配置类型选项列表
const ssidConfigTypeList = ref([
  {
    label: t('Config.Mode.DefaultConfig'),
    value: '0',
  },
  {
    label: t('Config.Mode.MultiSSID'),
    value: '1',
  },
])

// 多SSID配置相关状态
const ssid2gCount = ref(1) // 2.4G SSID数量，默认1个
const ssid5gCount = ref(1) // 5G SSID数量，默认1个

// 增加SSID
const addSSID2G = () => {
  if (ssid2gCount.value < 2) {
    ssid2gCount.value++
  }
}

const addSSID5G = () => {
  if (ssid5gCount.value < 2) {
    ssid5gCount.value++
  }
}

// 删除SSID
const removeSSID2G = () => {
  if (ssid2gCount.value > 1) {
    ssid2gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_2g2 = ''
    templateForm.key_2g2 = ''
    templateForm.vlanId24G2 = ''
    templateForm.wifiMaxsta_2G2 = ''
    templateForm.wifiApIsolate_2G2 = '0'
  }
}

const removeSSID5G = () => {
  if (ssid5gCount.value > 1) {
    ssid5gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_5g2 = ''
    templateForm.key_5g2 = ''
    templateForm.vlanId5G2 = ''
    templateForm.wifiMaxsta_5G2 = ''
    templateForm.wifiApIsolate_5G2 = '0'
  }
}

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { label: '255.0.0.0/8', value: '255.0.0.0' },
  { label: '255.128.0.0/9', value: '255.128.0.0' },
  { label: '255.192.0.0/10', value: '255.192.0.0' },
  { label: '255.224.0.0/11', value: '255.224.0.0' },
  { label: '255.240.0.0/12', value: '255.240.0.0' },
  { label: '255.248.0.0/13', value: '255.248.0.0' },
  { label: '255.252.0.0/14', value: '255.252.0.0' },
  { label: '255.254.0.0/15', value: '255.254.0.0' },

  // B类网络常用
  { label: '255.255.0.0/16', value: '255.255.0.0' },
  { label: '255.255.128.0/17', value: '255.255.128.0' },
  { label: '255.255.192.0/18', value: '255.255.192.0' },
  { label: '255.255.224.0/19', value: '255.255.224.0' },
  { label: '255.255.240.0/20', value: '255.255.240.0' },
  { label: '255.255.248.0/21', value: '255.255.248.0' },
  { label: '255.255.252.0/22', value: '255.255.252.0' },
  { label: '255.255.254.0/23', value: '255.255.254.0' },

  // C类网络常用
  { label: '255.255.255.0/24', value: '255.255.255.0' },
  { label: '255.255.255.128/25', value: '255.255.255.128' },
  { label: '***************/26', value: '***************' },
  { label: '***************/27', value: '***************' },
  { label: '***************/28', value: '***************' },
  { label: '***************/29', value: '***************' },
  { label: '***************/30', value: '***************' },

  // 特殊用途
  { label: '***************/31', value: '***************' },
  { label: '***************/32', value: '***************' },
]

const networkList = [
  {
    label: t('NetworkConfig.Network.DynamicIP'),
    value: 0,
  },
  {
    label: t('NetworkConfig.Network.PPPoE'),
    value: 1,
  },
  {
    label: t('NetworkConfig.Network.StaticIP'),
    value: 2,
  },
]
</script>

<template>
  <div>
    <VCard class="mb-6">
      <template #title>
        <div
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
"
        >
          <div class="mr-2">
            {{ t('Config.Mode.TemplateManagement') }}
            <BtnGroupSelector
              v-model:value="filterType"
              class="ml-5"
              fill-row
              :options="filterTypeList"
            />
          </div>

          <div class="gap-4 d-flex flex-wrap">
            <!-- 新建配置模板按钮改为带菜单的按钮 -->
            <VMenu>
              <template #activator="{ props }">
                <VBtn
                  color="success"
                  prepend-icon="tabler-layout-grid-add"
                  variant="tonal"
                  v-bind="props"
                >
                  {{ t('Config.Mode.NewConfigTemplate') }}
                </VBtn>
              </template>
              <VList>
                <VListItem
                  v-for="item in actionTypeList"
                  :key="item.value"
                  @click="() => { actionType = item.value; createNewTemplate(); }"
                >
                  {{ item.label }}
                </VListItem>
              </VList>
            </VMenu>
          </div>
        </div>
      </template>

      <VDivider />
      <VDataTableServer
        v-if="filterType.value == '1'"
        v-model:items-per-page="itemsPerPage"
        v-model:model-value="selectedRows"
        v-model:page="page"
        :headers="headers"
        :items="templateList"
        :items-length="totalTemplates"
        :no-data-text="t('NoData')"
        class="text-no-wrap"
      >
        <template #item.config="{ item }">
          <div
            v-if="item.config"
            class="d-flex align-center"
          >
            <VChip
              class="mr-2"
              color="success"
              size="small"
              variant="outlined"
            >
              {{ item.config.tpl_bands }}
            </VChip>
            {{ item.config.ssid_2g || '--' }} /{{ item.config.ssid_5g || '--' }}
          </div>
          <VChip
            v-else
            color="error"
            size="small"
            variant="outlined"
          >
            {{ t('Config.Mode.Off') }}
          </VChip>
        </template>
        <template #item.model="{ item }">
          {{ item.model ? item.model : "--" }}
        </template>
        <template #item.utime="{ item }">
          {{ item.utime ? formatTime(item.utime) : "--" }}
        </template>
        <template #item.deviceNum="{ item }">
          <div
            class="text-primary cursor-pointer associated-device"
            @click="deviceDetail(item)"
          >
            {{ item.associated_devices || 0 }}
          </div>
        </template>
        <template #item.vlanNum="{ item }">
          {{ item.vlanNum || 0 }}
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="editTemplate(item)">
                  {{ t('Config.Mode.EditTemplate') }}
                </VListItem>
                <VListItem @click="copyTemplate(item)">
                  {{ t('Config.Mode.CopyTemplate') }}
                </VListItem>
                <VListItem @click="deleteTemplate(item)">
                  {{ t('Config.Mode.DeleteTemplate') }}
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalTemplates"
          />
        </template>
      </VDataTableServer>

      <VDataTableServer
        v-else
        v-model:items-per-page="itemsPerPage"
        v-model:model-value="selectedRows"
        v-model:page="page"
        :headers="headers"
        :items="templateList"
        :items-length="totalTemplates"
        :no-data-text="t('NoData')"
        class="text-no-wrap"
      >
        <template #item.config="{ item }">
          <div
            v-if="item.config"
            class="d-flex align-center"
          >
            <VChip
              class="mr-2"
              color="success"
              size="small"
              variant="outlined"
            >
              {{ item.config.tpl_bands }}
            </VChip>
            {{ item.config.ssid_2g || '--' }} /{{ item.config.ssid_5g || '--' }}
          </div>
          <VChip
            v-else
            color="error"
            size="small"
            variant="outlined"
          >
            {{ t('Config.Mode.Off') }}
          </VChip>
        </template>
        <template #item.model="{ item }">
          {{ item.model ? item.model : "--" }}
        </template>
        <template #item.utime="{ item }">
          {{ item.utime ? formatTime(item.utime) : "--" }}
        </template>
        <template #item.deviceNum="{ item }">
          <div
            class="text-primary cursor-pointer associated-device"
            @click="deviceDetail(item)"
          >
            {{ item.associated_devices || 0 }}
          </div>
        </template>
        <template #item.vlanNum="{ item }">
          {{ item.vlanNum || 0 }}
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="editTemplate(item)">
                  {{ t('Config.Mode.EditTemplate') }}
                </VListItem>
                <VListItem @click="copyTemplate(item)">
                  {{ t('Config.Mode.CopyTemplate') }}
                </VListItem>
                <VListItem @click="deleteTemplate(item)">
                  {{ t('Config.Mode.DeleteTemplate') }}
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalTemplates"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <!-- 新建模板 -->
    <VNavigationDrawer
      v-if="createTemplateDialog"
      v-model="createTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ dialogStatus === 1 ? t('Config.Mode.EditTemplate') : t('Config.Mode.NewConfigTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="createTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 - flex-grow-1确保占据剩余空间 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 标签页固定 -->
          <div class="flex-shrink-0 px-6 pt-4">
            <VTabs
              v-model="currentTab"
              class="mb-4"
              align-tabs="center"
              fixed-tabs
            >
              <VTab
                v-for="(item, index) in tabList"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </VTab>
            </VTabs>
          </div>

          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <VWindow v-model="currentTab">
              <!-- 选择型号 -->
              <VWindowItem :value="0">
                <VForm ref="formRef1">
                  <!-- 模板名称输入框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TemplateName') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.tpl_name"
                      :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterTemplateName')), (v: string) => {
                        if (!isByteLengthInRange(v, 1, 64)) {
                          return t('Config.Mode.TemplateNameLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.EnterTemplateName')"
                    />
                  </div>
                  <!-- 备注输入框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Remark') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.description"
                      :rules="[(v: string) => {
                        if (!isByteLengthInRange(v, 0, 128)) {
                          return t('Config.Mode.RemarkLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectRemark')"
                    />
                  </div>
                  <!-- AP型号选择下拉框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.SelectModel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.model"
                      class="mb-4"
                      :placeholder="t('Config.Mode.SelectAPModel')"
                      :items="modelList"
                      item-title="label"
                      item-value="value"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <!-- 基础配置 -->
              <VWindowItem :value="1">
                <VForm ref="formRef2">
                  <!-- SSID配置类型选择按钮组 -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <BtnGroupSelector
                      v-model:value="templateForm.ssidConfigType"
                      fill-row
                      :options="ssidConfigTypeList"
                      @update:value="ssidConfigTypeChange"
                    />
                  </div>

                  <!-- 双频合一开关 (仅默认配置显示) -->
                  <div
                    v-if="templateForm.ssidConfigType === '0'"
                    class="d-flex justify-space-between align-center mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90">
                      {{ t('Config.AP.DualBandUnify') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.ssid_type"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                        @update:model-value="ssidTypeChange"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ t('Config.AP.DualBandUnifyHint') }}
                      </span>
                    </div>
                  </div>
                  <!-- 默认配置 - 双频合一 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1'">
                    <!-- 双频合一SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 双频合一加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.encryptionType"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                        item-title="label"
                        item-value="value"
                      />
                    </div>
                    <!-- 双频合一密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.password"
                        :append-inner-icon="showMergePassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePassword]"
                        :type="showMergePassword ? 'text' : 'password'"
                        @click:append-inner="
                          showMergePassword = !showMergePassword
                        "
                      />
                    </div>
                    <!-- 双频合一客户端隔离开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Isolate') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.isolate"
                          class="mr-2"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{ templateForm.isolate ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <VDivider class="mb-4" />
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-primary text-h5">
                      {{ t('Config.Mode.WirelessSettings2G') }}
                    </div>
                    <VBtn
                      v-if="templateForm.ssidConfigType === '1' && ssid2gCount < 2"
                      color="primary"
                      size="small"
                      variant="outlined"
                      @click="addSSID2G"
                    >
                      {{ t('Config.Mode.AddSSID') }}
                    </VBtn>
                  </div>

                  <!-- 默认配置 - 2.4G分开模式 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                    <!-- 启用Wi-Fi开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_2G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                    <!-- SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_2g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 2.4G加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_2G"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                      />
                    </div>
                    <!-- 2.4G密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_2g"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePasswordEight]"
                        :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :type="show2GPassword ? 'text' : 'password'"
                        @click:append-inner="show2GPassword = !show2GPassword"
                      />
                    </div>
                  </div>

                  <!-- 多SSID配置 - 2.4G WiFi开关 -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 2.4G WiFi总开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_2G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                  </div>

                  <!-- 通用2.4G设置 -->
                  <!-- 2.4G协议选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_2G"
                      :items="PROTOCOL_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectProtocol')"
                    />
                  </div>
                  <!-- 2.4G国家码选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiCountry_2G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectCountry')"
                      @update:model-value="() => {
                        templateForm.wifiChannel_2G = 'auto'
                      }"
                    />
                  </div>
                  <!-- 2.4G信道选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_2G"
                      :items="channelOptions2g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectChannel')"
                    />
                  </div>
                  <!-- 2.4G信道带宽选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="selectedBandwidth"
                      :items="BAND_WIDTH_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectBandwidth')"
                    />
                  </div>
                  <!-- 2.4G发射功率选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_2G"
                      :items="TX_POWER_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.Mode.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectTxPower')"
                    />
                  </div>
                  <!-- 2.4G最大连接数输入 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField v-model="templateForm.wifiMaxsta_2G" />
                  </div>

                  <!-- 多SSID配置卡片 - 2.4G -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 2.4G SSID 1 -->
                    <VCard
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID2G1') }}</span>
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEightMore]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId24G1"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId24G1', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_2G"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_2G"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>

                    <!-- 2.4G SSID 2 -->
                    <VCard
                      v-if="ssid2gCount >= 2"
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID2G2') }}</span>
                        <VBtn
                          color="error"
                          size="small"
                          variant="text"
                          icon="tabler-trash"
                          @click="removeSSID2G"
                        />
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g2"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g2"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEightMore]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId24G2"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId24G2', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_2G2"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_2G2"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_2G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>
                  <!-- 2.4G客户端隔离开关（仅默认配置分开模式显示） -->
                  <div
                    v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Isolate') }}
                    </div>
                    <VSwitch
                      v-model="templateForm.wifiApIsolate_2G"
                      false-value="0"
                      true-value="1"
                      :label="templateForm.wifiApIsolate_2G === '0' ? t('Config.Mode.Off') : t('Config.Mode.On')"
                    />
                  </div>
                  <VDivider class="mb-4" />
                  <!-- 5G -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-primary text-h5">
                      {{ t('Config.Mode.WirelessSettings5G') }}
                    </div>
                    <VBtn
                      v-if="templateForm.ssidConfigType === '1' && ssid5gCount < 2"
                      color="primary"
                      size="small"
                      variant="outlined"
                      @click="addSSID5G"
                    >
                      {{ t('Config.Mode.AddSSID') }}
                    </VBtn>
                  </div>

                  <!-- 默认配置 - 5G分开模式 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                    <!-- 5G WiFi开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.wifiOnOff_5G"
                          class="mr-2"
                          false-value="1"
                          true-value="0"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{
                            templateForm.wifiOnOff_5G === "0" ? t('Config.Mode.On') : t('Config.Mode.Off')
                          }}
                        </span>
                      </div>
                    </div>
                    <!-- 5G SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_5g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 5G加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_5G"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                          if (v === null) {
                            return t('Config.Mode.SelectEncryption')
                          }
                        }] : []"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                      />
                    </div>
                    <!-- 5G密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_5g"
                        :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :rules="[validatePasswordEight5G]"
                        :type="show5GPassword ? 'text' : 'password'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        @click:append-inner="show5GPassword = !show5GPassword"
                      />
                    </div>
                  </div>

                  <!-- 多SSID配置 - 5G WiFi开关 -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 5G WiFi总开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_5G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_5G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                  </div>

                  <!-- 通用5G设置 -->
                  <!-- 5G协议选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_5G"
                      :items="PROTOCOL_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectProtocol')"
                    />
                  </div>
                  <!-- 5G国家码选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiCountry_5G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectCountry')"
                      @update:model-value="() => {
                        templateForm.wifiChannel_5G = 'auto'
                      }"
                    />
                  </div>
                  <!-- 5G信道选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_5G"
                      :items="channelOptions5g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectChannel')"
                      @update:model-value="changeChannel"
                    />
                  </div>
                  <!-- 5G信道带宽选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHtMode_5G"
                      :items="BAND_WIDTH_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectBandwidth')"
                    />
                  </div>
                  <!-- 5G发射功率选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_5G"
                      :items="TX_POWER_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.Mode.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectTxPower')"
                    />
                  </div>
                  <!-- 5G最大连接数输入 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField v-model="templateForm.wifiMaxsta_5G" />
                  </div>

                  <!-- 多SSID配置卡片 - 5G -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 5G SSID 1 -->
                    <VCard
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID5G1') }}</span>
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight5GMore]"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show5GPassword ? 'text' : 'password'"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId5G1"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId5G1', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_5G"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>

                    <!-- 5G SSID 2 -->
                    <VCard
                      v-if="ssid5gCount >= 2"
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID5G2') }}</span>
                        <VBtn
                          color="error"
                          size="small"
                          variant="text"
                          icon="tabler-trash"
                          @click="removeSSID5G"
                        />
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g2"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g2"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight5GMore]"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show5GPassword ? 'text' : 'password'"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId5G2"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId5G2', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_5G2"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G2"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_5G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>

                  <!-- 5G客户端隔离开关（仅默认配置分开模式显示） -->
                  <div
                    v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type == '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Isolate') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifiApIsolate_5G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{
                          templateForm.wifiApIsolate_5G === "0" ? t('Config.Mode.Off') : t('Config.Mode.On')
                        }}
                      </span>
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="2">
                <VForm ref="formRef3">
                  <!-- 网络类型选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.NetworkType') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.net_type"
                      :items="NET_TYPE_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectNetworkType')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectNetworkType')"
                    />
                  </div>

                  <!-- VLAN配置（仅在网络类型为VLAN时显示） -->
                  <div
                    v-if="templateForm.net_type === '2'"
                    class="mb-4"
                  >
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.VLANID') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.vlanTemplateId"
                        :items="vlanList"
                        item-title="itemTitle"
                        item-value="id"
                        :placeholder="t('Config.Mode.SelectVLAN')"
                        :rules="[(v: string) => {
                          if (!v) {
                            return t('Config.Mode.SelectVLAN')
                          }
                          return true
                        }]"
                        @update:model-value="(value: any) => handleVlanSelect('vlanId', value)"
                      />
                    </div>
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.LANIP') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.ap_lan_ip"
                      :placeholder="t('Device.AP.Required')"
                    />
                  </div>

                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.LAN.SubnetMask') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.ap_lan_mask"
                      :items="subnetMaskOptions"
                      placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
                      item-title="label"
                      item-value="value"
                    />
                  </div>

                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.SpeedLimit') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.networkLimit"
                      :items="SPEED_LIMIT_TYPE_LIST"
                      disabled
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectSpeedLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.UpstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.upstreamLimit"
                      disabled
                      :placeholder="t('Config.Mode.EnterUpstreamLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.DownstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.downstreamLimit"
                      disabled
                      :placeholder="t('Config.Mode.EnterDownstreamLimit')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="3">
                <VForm ref="formRef4">
                  <!-- 快速漫游开关 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.RoamingProtocol2G') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifi80211r_2G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ templateForm.wifi80211r_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                      </span>
                    </div>
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.RoamingProtocol5G') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifi80211r_5G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ templateForm.wifi80211r_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                      </span>
                    </div>
                  </div>
                  <!-- 漫游协议选择（仅在快速漫游开启时显示） -->
                  <div v-if="templateForm.wifi80211r_5G === '1' || templateForm.wifi80211r_2G === '1'">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.RoamingProtocol') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.roamingProtocol"
                        :items="ROAMING_PROTOCOL"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectRoamingProtocol')"
                        :rules="[(v: number | undefined) => {
                          if (v === undefined) {
                            return t('Config.Mode.SelectRoamingProtocol')
                          }
                          return true
                        }]"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSIDLoadBalancing') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.load_balance_interval"
                        class="mb-4"
                        :placeholder="t('Config.Mode.SSIDLoadBalancingPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.DisconnectWeakSignal') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.weak_signal_threshold"
                        class="mb-4"
                        :placeholder="t('Config.Mode.DisconnectWeakSignalPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.IgnoreWeakSignal') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ignore_weak_signal"
                        class="mb-4"
                        :placeholder="t('Config.Mode.IgnoreWeakSignalPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.IgnoreExcessiveRetransmission') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ignore_excessive_retransmission"
                        :placeholder="t('Config.Mode.IgnoreExcessiveRetransmissionPlaceholder')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.NumericRangeValidation'))]"
                      />
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
            </VWindow>
          </div>
        </div>

        <!-- 底部固定 -->
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-end">
          <VBtn
            color="secondary"
            variant="tonal"
            class="mr-2"
            @click="createTemplateDialog = false"
          >
            {{ t('Config.Mode.Cancel') }}
          </VBtn>
          <VBtn
            v-if="currentTab !== tabList.length - 1"
            color="primary"
            variant="flat"
            @click="nextStep"
          >
            {{ t('Config.Mode.Next') }}
          </VBtn>
          <VBtn
            v-else
            color="primary"
            variant="flat"
            @click="save"
          >
            {{ t('Config.Mode.SaveTemplate') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- AC模板新建/编辑抽屉 -->
    <VNavigationDrawer
      v-if="createACTemplateDialog"
      v-model="createACTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ dialogStatus === 1 ? t('Config.AP.EditACTemplate') : t('Config.AP.NewACTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="createACTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 标签页固定 -->
          <div class="flex-shrink-0 px-6 pt-4">
            <VTabs
              v-model="currentACTab"
              class="mb-4"
              align-tabs="center"
              fixed-tabs
            >
              <VTab
                v-for="(item, index) in acTabList"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </VTab>
            </VTabs>
          </div>

          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <VWindow v-model="currentACTab">
              <!-- 基础设置 -->
              <VWindowItem :value="0">
                <VForm ref="acFormRef1">
                  <!-- 模板名称 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TemplateName') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.name"
                      :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterTemplateName')), (v: string) => {
                        if (!isByteLengthInRange(v, 1, 64)) {
                          return t('Config.Mode.TemplateNameLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.EnterTemplateName')"
                    />
                  </div>

                  <!-- AC型号选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.SelectACModel') }}
                    </div>
                    <AppSelect
                      v-model="acTemplateForm.model"
                      class="mb-4"
                      :placeholder="t('Config.AP.SelectACModel')"
                      :items="acModelList"
                      item-title="label"
                      item-value="value"
                    />
                  </div>

                  <!-- 备注 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Remark') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.remark"
                      :rules="[(v: string) => {
                        if (!isByteLengthInRange(v, 0, 128)) {
                          return t('Config.Mode.RemarkLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectRemark')"
                    />
                  </div>
                </VForm>
              </VWindowItem>

              <!-- 网络设置 -->
              <VWindowItem :value="1">
                <VForm ref="acFormRef2">
                  <!-- 网络类型选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.NetworkType') }}
                    </div>
                    <AppSelect
                      v-model="acTemplateForm.networkConfig.networkType"
                      :items="networkList"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectNetworkType')"
                    />
                  </div>

                  <!-- MTU设置 - 所有模式都可填 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.AP.MTU') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.mtu"
                      :placeholder="t('Config.AP.EnterMTU')"
                      type="number"
                    />
                  </div>

                  <!-- PPPoE 模式字段 -->
                  <div v-if="acTemplateForm.networkConfig.networkType === 1">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.InternetAccount') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanUsername"
                        :rules="[
                          (v: string) => requiredValidator(v, t('NetworkConfig.Network.InternetAccountRequired')),
                          (v: string) => validateAccountAndPassword(v) ? true : t('NetworkConfig.Network.InternetAccountInvalid'),
                        ]"
                        :placeholder="t('NetworkConfig.Network.EnterInternetAccount')"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.InternetPassword') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanPassword"
                        :rules="[
                          (v: string) => requiredValidator(v, t('NetworkConfig.Network.InternetPasswordRequired')),
                          (v: string) => validateAccountAndPassword(v) ? true : t('NetworkConfig.Network.InternetPasswordInvalid'),
                        ]"
                        :placeholder="t('NetworkConfig.Network.EnterInternetPassword')"
                        type="password"
                      />
                    </div>
                  </div>

                  <!-- 静态IP 模式字段 -->
                  <div v-if="acTemplateForm.networkConfig.networkType === 2">
                    <!-- IP地址 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.IPAddress') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanIpAddress"
                        :rules="[
                          (v: string) => requiredValidator(v, t('NetworkConfig.Network.IPAddressRequired')),
                          (v: string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.IPAddressInvalid'),
                        ]"
                        :placeholder="t('NetworkConfig.Network.EnterIPAddress')"
                      />
                    </div>
                    <!-- 网关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.Gateway') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanGateway"
                        :rules="[
                          (v: string) => requiredValidator(v, t('NetworkConfig.Network.GatewayRequired')),
                          (v: string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.GatewayInvalid'),
                        ]"
                        :placeholder="t('NetworkConfig.Network.EnterGateway')"
                      />
                    </div>
                    <!-- 子网掩码 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.Subnet') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanNetmask"
                        :rules="[
                          (v: string) => requiredValidator(v, t('NetworkConfig.Network.SubnetRequired')),
                          (v: string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.SubnetInvalid'),
                        ]"
                        :placeholder="t('NetworkConfig.Network.EnterSubnet')"
                      />
                    </div>

                    <!-- DNS服务器设置 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.PrimaryDNS') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanDns[0]"
                        :placeholder="t('NetworkConfig.Network.EnterPrimaryDNS')"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('NetworkConfig.Network.SecondaryDNS') }}
                      </div>
                      <AppTextField
                        v-model="acTemplateForm.networkConfig.wanDns[1]"
                        :placeholder="t('NetworkConfig.Network.EnterSecondaryDNS')"
                      />
                    </div>
                  </div>
                </VForm>
              </VWindowItem>

              <!-- LAN设置 -->
              <VWindowItem :value="2">
                <VForm ref="acFormRef3">
                  <!-- VLAN配置开关 -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div>
                      <div class="text-subtitle-2 text-on-surface opacity-90 mb-2">
                        {{ t('Config.Mode.ManagerVLAN') }}
                      </div>
                    </div>
                    <VSwitch
                      v-model="acTemplateForm.lanConfig.vlanEnabled"
                      true-value="1"
                      false-value="0"
                    />
                  </div>

                  <!-- VLAN配置（仅在启用时显示） -->
                  <div v-if="acTemplateForm.lanConfig.vlanEnabled === '1'">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.VLANConfig') }}
                      </div>
                      <AppSelect
                        v-model="acTemplateForm.lanConfig.vlanTemplateId"
                        :items="vlanList"
                        :rules="[vlanValidator]"
                        item-title="itemTitle"
                        item-value="id"
                        :placeholder="t('Config.Mode.SelectVLAN')"
                      />
                    </div>
                  </div>
                  <!-- LAN IP地址 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.LAN.GatewayIP') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.lanConfig.lanIpAddress"
                      :rules="[lanIpValidator]"
                      :placeholder="t('NetworkConfig.LAN.EnterGatewayIP')"
                    />
                  </div>

                  <!-- 子网掩码 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.LAN.SubnetMask') }}
                    </div>
                    <AppSelect
                      v-model="acTemplateForm.lanConfig.lanNetmask"
                      :items="subnetMaskOptions"
                      :rules="[subnetMaskValidator]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
                    />
                  </div>

                  <!-- DHCP服务开关 -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div>
                      <div class="text-subtitle-2 text-on-surface opacity-90 mb-2">
                        {{ t('NetworkConfig.LAN.DHCPService') }}
                      </div>
                      <div class="text-subtitle-2 text-on-surface opacity-50">
                        {{ t('NetworkConfig.LAN.EnableDHCPDesc') }}
                      </div>
                    </div>
                    <VSwitch
                      v-model="acTemplateForm.lanConfig.dhcpDisabled"
                      true-value="0"
                      false-value="1"
                    />
                  </div>

                  <!-- DHCP配置（仅在启用时显示） -->
                  <div v-if="acTemplateForm.lanConfig.dhcpDisabled === '0'">
                    <VCard
                      class="mt-4"
                      variant="outlined"
                    >
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('NetworkConfig.LAN.StartValue') }}
                          </div>
                          <div class="flex-grow-1">
                            <AppTextField
                              v-model="acTemplateForm.lanConfig.dhcpStartValue"
                              :rules="[dhcpValidator]"
                              :placeholder="t('NetworkConfig.LAN.EnterStartValue')"
                              type="number"
                              step="1"
                              min="1"
                              @keydown="preventDecimal"
                              @input="e => handleIntegerInput(e, 'dhcpStartValue')"
                            />
                            <div class="text-subtitle-2 text-on-surface opacity-50 mt-1">
                              {{ t('NetworkConfig.LAN.StartIPDesc') }}
                            </div>
                          </div>
                        </div>

                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('NetworkConfig.LAN.MaxCount') }}
                          </div>
                          <AppTextField
                            v-model="acTemplateForm.lanConfig.dhcpMaxNumber"
                            :rules="[dhcpMaxValidator]"
                            :placeholder="t('NetworkConfig.LAN.EnterMaxCount')"
                            type="number"
                            step="1"
                            min="1"
                            @keydown="preventDecimal"
                            @input="e => handleIntegerInput(e, 'dhcpMaxNumber')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>
                </VForm>
              </VWindowItem>

              <!-- 系统配置 -->
              <VWindowItem :value="3">
                <VForm ref="acFormRef4">
                  <!-- 当前时间 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="flex-grow-1">
                      <VCard
                        variant="outlined"
                        class="pa-3"
                        style="background-color: #f5f5f5;"
                      >
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('SystemConfig.System.CurrentTime') }}
                        </div>
                        <div class="text-success text-h6">
                          {{ currentTime }} {{ currentTimezone }}
                        </div>
                      </VCard>
                    </div>
                  </div>

                  <!-- 管理员密码 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('SystemConfig.System.AdminPassword') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.systemConfig.password"
                      :rules="[(v: string) => requiredValidator(v, t('SystemConfig.System.EnterAdminPassword'))]"
                      :placeholder="t('SystemConfig.System.EnterAdminPassword')"
                      :append-inner-icon="showAdminPassword ? 'tabler-eye-off' : 'tabler-eye'"
                      :type="showAdminPassword ? 'text' : 'password'"
                      @click:append-inner="showAdminPassword = !showAdminPassword"
                    />
                  </div>

                  <!-- 时区 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('SystemConfig.System.Timezone') }}
                    </div>
                    <AppSelect
                      v-model="acTemplateForm.systemConfig.timeZone"
                      :items="timezoneList"
                      :rules="[(v: string) => requiredValidator(v, t('SystemConfig.System.SelectTimezone'))]"
                      item-title="label"
                      :placeholder="t('SystemConfig.System.SelectTimezone')"
                    />
                  </div>

                  <!-- 设备名称 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('SystemConfig.System.DeviceName') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.systemConfig.hostName"
                      :rules="[(v: string) => requiredValidator(v, t('SystemConfig.System.EnterDeviceName'))]"
                      :placeholder="t('SystemConfig.System.EnterDeviceName')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
            </VWindow>
          </div>
        </div>

        <!-- 底部固定按钮 -->
        <div class="flex-shrink-0 d-flex justify-end gap-3 pa-4">
          <VBtn
            color="secondary"
            variant="outlined"
            @click="createACTemplateDialog = false"
          >
            {{ t('Config.Mode.Cancel') }}
          </VBtn>
          <VBtn
            v-if="currentACTab < 3"
            color="primary"
            @click="nextStepAC"
          >
            {{ t('Config.Mode.Next') }}
          </VBtn>
          <VBtn
            v-else
            color="primary"
            @click="saveACTemplate"
          >
            {{ t('Config.Mode.SaveTemplate') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped>
.w-80px {
  inline-size: 80px;
}

.line-height-38px {
  line-height: 38px;
}

.flexBox {
  display: flex;
  align-items: center;
}

.associated-device {
  &:hover {
    text-decoration: underline;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
