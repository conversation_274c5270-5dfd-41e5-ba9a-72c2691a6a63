<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { computed, onBeforeUnmount, onMounted, reactive, ref, useTemplateRef, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import BtnGroupSelector from '@/components/network/BtnGroupSelector.vue'
import {
  CHANNEL_ARR_2G,
  CHANNEL_ARR_5G,
  COUNTRY_OPTIONS,
  NET_TYPE,
  ROAMING_PROTOCOL,
  SPEED_LIMIT_TYPE,
  TEMPLATE_DISTRIBUTION_METHOD,
  TX_POWER_2G,
  TX_POWER_5G,
} from '@/utils/constants'
import { isEmptyArray, isNullOrUndefined } from '@core/utils/helpers'
import { requiredValidator } from '@core/utils/validators'
import { isByteLengthInRange } from '@layouts/utils'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const deviceName = ref('')

// 对constants中的国际化常量进行本地化处理
const COUNTRY_OPTIONS_LOCALIZED = computed(() => {
  return COUNTRY_OPTIONS.map(item => ({
    ...item,
    label: item.label, // 国家代码不需要翻译
  }))
})

// 对NET_TYPE进行本地化处理
const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => {
    if (item.label.startsWith('NetworkConfig.Modes.')) {
      return {
        ...item,
        label: t(item.label), // 网络类型需要翻译
      }
    }

    return item
  })
})

// 对SPEED_LIMIT_TYPE进行本地化处理
const SPEED_LIMIT_TYPE_LIST = computed(() => {
  return SPEED_LIMIT_TYPE.map(item => ({
    ...item,
    label: t(item.label), // 限速类型需要翻译
  }))
})

// 对TEMPLATE_DISTRIBUTION_METHOD进行本地化处理
const TEMPLATE_DISTRIBUTION_METHOD_LOCALIZED = computed(() => {
  return TEMPLATE_DISTRIBUTION_METHOD.map(item => {
    if (item.label === 'template.immediate')
      return { ...item, label: t('template.immediate') }

    return item
  })
})

// 对TX_POWER_2G进行本地化处理
const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => {
    if (item.label === 'txPower.penetration')
      return { ...item, label: t('txPower.penetration') }
    else if (item.label === 'txPower.normal')
      return { ...item, label: t('txPower.normal') }
    else if (item.label === 'txPower.saving')
      return { ...item, label: t('txPower.saving') }

    return item
  })
})

// 对TX_POWER_5G进行本地化处理
const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => {
    if (item.label === 'txPower.penetration')
      return { ...item, label: t('txPower.penetration') }
    else if (item.label === 'txPower.normal')
      return { ...item, label: t('txPower.normal') }
    else if (item.label === 'txPower.saving')
      return { ...item, label: t('txPower.saving') }

    return item
  })
})

// 对ENCRYPTION_TYPE进行本地化处理
const ENCRYPTION_TYPE_LOCALIZED = computed(() => {
  return ENCRYPTION_TYPE.map(item => ({
    ...item,
    label: item.label === 'NONE' ? t('Config.Mode.None') : item.label, // 只有None需要翻译，其他是技术标准
  }))
})

// 对PROTOCOL_2G进行本地化处理
const PROTOCOL_2G_LOCALIZED = computed(() => {
  return PROTOCOL_2G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

// 对PROTOCOL_5G进行本地化处理
const PROTOCOL_5G_LOCALIZED = computed(() => {
  return PROTOCOL_5G.map(item => ({
    ...item,
    label: item.label, // 协议不需要翻译
  }))
})

// 对BAND_WIDTH_2G进行本地化处理
const BAND_WIDTH_2G_LOCALIZED = computed(() => {
  return BAND_WIDTH_2G.map(item => ({
    ...item,
    label: item.label, // 带宽值不需要翻译
  }))
})

const selectedModel = ref()
const selectedRows = ref([])

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)

// 表头
const headers = [
  { title: t('Config.AP.DeviceName'), key: 'name', sortable: false },
  { title: t('Config.AP.Model'), key: 'model', sortable: false },
  { title: t('Config.AP.SerialNumber'), key: 'sn', sortable: false },
  { title: t('Config.AP.MACAddress'), key: 'mac', sortable: false },
  { title: t('Config.AP.Status'), key: 'onoff', sortable: false },
]

const templateList = ref([])

// 数据
const apList = ref({
  total: 0,
  list: [],
})

// 动态计算表格数据
const list = computed((): any[] => {
  let arr = apList.value.list

  // 设备名筛选
  if (deviceName.value) {
    arr = arr.filter((item: any) =>
      item.user_name && item.user_name.toLowerCase().includes(deviceName.value.toLowerCase()),
    )
  }

  // 型号筛选
  if (selectedModel.value)
    arr = arr.filter((item: any) => item.model === selectedModel.value)

  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return arr.slice(start, end)
})

//  监听list  变化设置 selectedRows.value = []
watch(list, () => {
  selectedRows.value = []
})

// 监听型号选择变化，自动全选该型号的设备
watch(selectedModel, newModel => {
  if (newModel) {
    // 获取当前页面中该型号的所有设备
    const modelDevices = list.value.filter((item: any) => item.model === newModel)

    selectedRows.value = modelDevices.map((item: any) => item.sn)
  }
  else {
    // 如果没有选择型号，清空选择
    selectedRows.value = []
  }
})

const totalProduct = computed(() => {
  return apList.value.total
})

const distributeTemplateDialog = ref(false)

// 下发配置相关变量
const step = ref(0)
const formRef1 = useTemplateRef('formRef1')
const formRef2 = useTemplateRef('formRef2')

const formRef3 = ref()
const formRef4 = ref()
const formRef = [formRef1, formRef2, formRef3, formRef4]

const distributeForm = reactive({
  templateId: null,
  distributeType: null,
  distributeTime: '',
  distributeTimeType: '0',
})

const distributeDisabled = computed(() => {
  if (distributeForm.distributeType === null)
    return true

  if (distributeForm.distributeType === 1)
    return !distributeForm.distributeTime
  else
    return false
})

const resetDistributeForm = () => {
  distributeForm.templateId = null
  distributeForm.distributeType = null
  distributeForm.distributeTime = ''
  distributeForm.distributeTimeType = '0'
}

// 0-未开始 1-进行中 2-完成
const distributeState = ref(0)
const distributeProgress = ref(0)
const totalNum = ref(0)
const successNum = ref(0)
const failNum = ref(0)
const exportData = ref([])

// 添加计时器变量
const startTime = ref(0)

const requestId = ref('')

// 新增进度显示对话框
const showProgressDialog = ref(false)

const openDistributeTemplateDialog = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.error(t('Config.AP.SelectDevices'))

    return
  }

  // 检查选中的设备是否有多个型号
  const selectedDevices = list.value.filter((item: any) => selectedRows.value.includes(item.sn))
  const deviceModels = new Set(selectedDevices.map((item: any) => item.model))

  if (deviceModels.size > 1) {
    ElMessage.error(t('Config.AP.MultipleModelsNotSupported'))

    return
  }

  resetDistributeForm()

  // 根据设备类型获取对应的模板列表
  const templateApiUrl = dType.value === 0 ? '/v1/apTemplate' : '/v1/acTemplate'

  $get(templateApiUrl, { page: 1, size: 1000 }).then(res => {
    if (res.msg === 'success')
      templateList.value = res.result.rows || []
  })
  step.value = 0
  distributeProgress.value = 0
  successNum.value = 0
  failNum.value = 0
  distributeTemplateDialog.value = true
}

const closeProgressDialog = () => {
  showProgressDialog.value = false
  distributeState.value = 0
  startTime.value = 0
}

// 下发配置
const distributeHandle = () => {
  if (!distributeDisabled.value) {
    const data = {
      deviceSns: selectedRows.value,
    }

    // 根据设备类型选择对应的下发API
    const emitApiUrl = dType.value === 0
      ? `/v1/apTemplate/emit/${distributeForm.templateId}`
      : `/v1/acTemplate/emit/${distributeForm.templateId}`

    $post(emitApiUrl, data).then(res => {
      totalNum.value = selectedRows.value.length
      console.log('distributeHandle', res)
      if (res.msg === 'success') {
        requestId.value = res.result.bulkId
        distributeState.value = 3

        // 关闭右侧划出区域
        distributeTemplateDialog.value = false

        // 打开进度显示对话框
        showProgressDialog.value = true

        // 开始虚假进度显示
        startFakeProgress()
      }
    })
  }
}

// 虚假进度显示函数
const startFakeProgress = () => {
  // 重置进度
  distributeProgress.value = 0

  // 第1秒：显示20-30%的进度
  setTimeout(() => {
    if (distributeState.value === 3) { // 确保还在下发状态
      distributeProgress.value = Math.floor(Math.random() * 11) + 20 // 20-30
    }
  }, 1000)

  // 第2秒：显示60-70%的进度
  setTimeout(() => {
    if (distributeState.value === 3) { // 确保还在下发状态
      distributeProgress.value = Math.floor(Math.random() * 11) + 60 // 60-70
    }
  }, 2000)

  // 第3秒：显示100%的进度，然后请求结果
  setTimeout(() => {
    if (distributeState.value === 3) { // 确保还在下发状态
      distributeProgress.value = 100

      // 请求下发结果
      dealRequestData()
    }
  }, 3000)
}

const resultTime = ref('')

const dealRequestData = () => {
  // 首次调用时记录开始时间
  if (!startTime.value)
    startTime.value = Date.now()

  // 计算超时时间（AP数量*5秒）
  const timeout = totalNum.value * 5000
  const currentTime = Date.now()
  const elapsedTime = currentTime - startTime.value

  // 检查是否超时
  if (elapsedTime >= timeout) {
    distributeState.value = 2

    // 更新未完成的设备为失败状态
    const unfinishedNum = totalNum.value - successNum.value - failNum.value

    failNum.value = Math.floor(unfinishedNum)
    ElMessage.error(t('Config.AP.ConfigTimeout'))
    startTime.value = 0

    return
  }

  $get(`/v1/bulkCmdResult/${requestId.value}`, {}).then(res => {
    if (res.msg === 'success') {
      // 根据新的返回值格式处理
      const resultList = res.result.rows || []

      resultTime.value = moment(res.result.ext.ts * 1000 || '').format('YYYY-MM-DD HH:mm:ss')

      // 更新导出数据
      exportData.value = resultList.map((item: any) => ({
        sn: item.sn,
        status: item.result ? '2' : '99', // 成功为'2'，失败为'99'
      }))

      // 统计成功和失败数量
      successNum.value = resultList.filter((item: any) => item.result === true).length
      failNum.value = resultList.filter((item: any) => item.result === false).length

      // 保持进度为100%（由虚假进度控制）
      // distributeProgress.value = Math.floor((successNum.value / totalNum.value) * 100)

      // 检查是否所有设备都已完成
      if (successNum.value + failNum.value === totalNum.value) {
        distributeState.value = 2

        if (successNum.value === totalNum.value)
          ElMessage.success(t('Config.AP.ConfigAllSuccess'))
        else if (successNum.value > 0)
          ElMessage.success(t('Config.AP.ConfigPartialSuccess'))
        else
          ElMessage.error(t('Config.AP.ConfigAllFailed'))

        startTime.value = 0
      }
      else {
        distributeState.value = 3

        // 检查剩余时间是否足够下一次轮询
        const remainingTime = timeout - elapsedTime
        if (remainingTime >= 1000) {
          setTimeout(() => {
            dealRequestData()
          }, 1000)
        }
        else {
          // 剩余时间不足，判定为超时
          distributeState.value = 2

          // 更新未完成的设备为失败状态
          failNum.value = Math.floor(totalNum.value - successNum.value)
          ElMessage.error(t('Config.AP.ConfigTimeout'))
          startTime.value = 0
        }
      }
    }
  }).catch(() => {
    distributeState.value = 2

    // 更新未完成的设备为失败状态
    failNum.value = Math.floor(totalNum.value - successNum.value)

    ElMessage.error(t('Config.AP.RequestFailed'))
    startTime.value = 0
  })
}

const distributeSuccess = () => {
  closeProgressDialog()
  step.value = 0
  distributeState.value = 0
  formRef1.value?.reset()
  formRef2.value?.reset()
}

const exportLog = () => {
  // 准备CSV数据
  const csvHeaders = [t('Config.AP.SerialNumber'), t('Config.AP.ConfigStatus'), t('Config.AP.ConfigTime')]

  // 调试信息
  console.log('exportData.value:', exportData.value)
  console.log('totalNum.value:', totalNum.value)
  console.log('successNum.value:', successNum.value)
  console.log('failNum.value:', failNum.value)

  // 直接使用所有导出数据，不再过滤selectedRows
  const arr = exportData.value

  // 过滤有效的状态数据（排除end和无效数据）
  const validStatusList = arr.filter((item: any) => item.sn && item.status !== undefined && item.sn.length === 20)
    .map((item: any) => ({
      sn: item.sn,
      statusText: item.status === '2' || item.status === '4'
        ? t('Config.AP.Success')
        : item.status === '0' ? t('Config.AP.NotStarted') : t('Config.AP.Failed'),
      time: resultTime.value || new Date().toLocaleString(),
    }))

  // 生成CSV内容，为序列号添加引号以防止Excel将其转换为科学计数法
  const csvContent = [
    csvHeaders.join(','),
    ...validStatusList.map(item => [
      `="${item.sn}"`, // 添加引号和等号，强制Excel将其作为文本处理
      item.statusText,
      `="${item.time}"`, // 为时间列也添加引号，确保Excel正确显示
    ].join(',')),
  ].join('\n')

  // 创建Blob对象
  const blob = new Blob([`\uFEFF${csvContent}`], {
    type: 'text/csv;charset=utf-8;',
  })

  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  // 设置文件名
  const fileName = `${t('Config.AP.ConfigDistributionRecord')}_${new Date().toLocaleDateString().replace(/\//g, '')}.csv`

  link.setAttribute('href', url)
  link.setAttribute('download', fileName)
  link.style.visibility = 'hidden'

  // 添加到页面并触发下载
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

const modelList = ref([] as { label: string; value?: string }[])

// 获取设备列表
const getDeviceList = () => {
  $get('/v1/device/list', {
    page: page.value,
    size: itemsPerPage.value,
    dType: dType.value, // 0: AP, 1: AC
  }).then(res => {
    if (res.msg === 'success' && res.result) {
      apList.value.total = res.result.count || 0
      apList.value.list = (res.result.rows || []).filter((item: any) => item.sn)
      apList.value.list = apList.value.list.map(item => ({
        ...item,
        type: dType.value === 0 ? 'AP' : 'AC',
      }))

      const mainList = apList.value.list
      const models = new Set([]) as Set<string>

      mainList.forEach((item: any) => {
        if (item.model)
          models.add(item.model)
      })
      modelList.value = Array.from(models).map(item => ({
        label: item,
        value: item,
      }))
    }
  })
}

let templateId = ''

const currentTab = ref(0)

const nextStepCreate = () => {
  formRef[currentTab.value].value?.validate().then(res => {
    if (res.valid)
      currentTab.value += 1
  })
}

const nextStep = async () => {
  const currentFormRef = [formRef1, formRef2, formRef3, formRef4][currentTab.value]
  const valid = await currentFormRef.value?.validate()
  if (valid?.valid)
    currentTab.value += 1
}

const tabList = ref([
  { label: t('Config.Mode.BasicSettings'), value: 0 },
  { label: t('Config.AP.SSIDConfig'), value: 1 },
  { label: t('Config.AP.AdvancedConfig'), value: 2 },
  { label: t('Config.AP.RoamingConfig'), value: 3 },
])

// SSID配置类型选项列表
const ssidConfigTypeList = ref([
  {
    label: t('Config.Mode.DefaultConfig'),
    value: '0',
  },
  {
    label: t('Config.Mode.MultiSSID'),
    value: '1',
  },
])

// 多SSID配置相关状态
const ssid2gCount = ref(1) // 2.4G SSID数量，默认1个
const ssid5gCount = ref(1) // 5G SSID数量，默认1个

// 密码显示状态
const showMergePassword = ref(false)

// SSID配置类型切换处理
const ssidConfigTypeChange = () => {
  formRef2.value?.resetValidation()

  // 切换到多SSID配置时，重置双频合一为分开模式
  if (templateForm.ssidConfigType === '1')
    templateForm.ssid_type = '0'
}

// SSID类型切换处理
const ssidTypeChange = () => {
  formRef2.value?.resetValidation()
}

// 增加SSID
const addSSID2G = () => {
  if (ssid2gCount.value < 2) {
    ssid2gCount.value++
  }
}

const addSSID5G = () => {
  if (ssid5gCount.value < 2) {
    ssid5gCount.value++
  }
}

// 删除SSID
const removeSSID2G = () => {
  if (ssid2gCount.value > 1) {
    ssid2gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_2g2 = ''
    templateForm.key_2g2 = ''
    templateForm.vlanId24G2 = ''
    templateForm.wifiMaxsta_2G2 = ''
    templateForm.wifiApIsolate_2G2 = '0'
  }
}

const removeSSID5G = () => {
  if (ssid5gCount.value > 1) {
    ssid5gCount.value--

    // 清空第二个SSID的数据
    templateForm.ssid_5g2 = ''
    templateForm.key_5g2 = ''
    templateForm.vlanId5G2 = ''
    templateForm.wifiMaxsta_5G2 = ''
    templateForm.wifiApIsolate_5G2 = '0'
  }
}

let templateForm = reactive({
  id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
  tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
  model: '', // AP型号
  tpl_bands: '', // 适用频段
  ssid_count: '', // SSID数量
  modified_at: '', //	最后修改时间
  description: '', //	备注，支持中文（UTF-8）一个中文占三字节
  ssid_2g: '', //	2G SSID
  ssid_5g: '', //	5G SSID
  key_2g: '', //	2G密码
  key_5g: '', //	5G密码
  ssid_type: '0', //	1：SSID双频合一 0：SSID分开
  ssidConfigType: '0', // 0：默认配置 1：多SSID配置
  net_type: undefined, //	0：路由模式 1：AP模式
  ap_lan_ip: '', //	LAN IP
  ap_lan_mask: '', //	LAN子网掩码
  wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
  wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
  wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
  wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
  wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
  wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
  wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
  wifiCountry_5G: 'CN', //	5.8G 国家代码
  wifiChannel_2G: 'auto', //	2.4G 信道
  wifiChannel_5G: 'auto', //	5.8G 信道
  wifiHwMode_2G: undefined, //	2.4G协议
  wifiHwMode_5G: undefined, //	5.8G协议
  wifiHtMode_2G: 'HT20', //	2.4G带宽
  wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
  wifiHtMode_5G: 'HT160', //	5.8G带宽
  wifiTxpower_2G: '', // 2.4G信号调节
  wifiTxpower_5G: '', // 5.8G信号调节
  wifi80211r_2G: '', //	2.4G快速漫游r协议
  wifi80211r_5G: '', //	5.8G快速漫游r协议
  wifiAuthmode_2G: undefined, //	2.4G加密方式选项
  wifiAuthmode_5G: undefined, //	5.8G加密方式选项
  wifiWpa3_2G: '', //	2.4G wpa3 开启标志
  wifiWpa3_5G: '', //	5.8G wpa3 开启标志
  // 双频合一的参数
  ssid: '', // SSID名称
  encryptionType: undefined, // 加密类型
  password: '', // 密码
  isolate: false, // 隔离
  // 漫游配置
  quickRoaming: false, // 快速漫游
  roamingProtocol: undefined, // 漫游协议
  // 多SSID配置字段
  ssid_2g2: '', // 2.4G第二个SSID
  key_2g2: '', // 2.4G第二个密码
  wifiMaxsta_2G2: '', // 2.4G第二个最大连接数
  wifiApIsolate_2G2: '0', // 2.4G第二个AP隔离
  ssid_5g2: '', // 5G第二个SSID
  key_5g2: '', // 5G第二个密码
  wifiMaxsta_5G2: '', // 5G第二个最大连接数
  wifiApIsolate_5G2: '0', // 5G第二个AP隔离
  // VLAN配置字段
  vlanTemplateId: '', // VLAN模板ID
  vlanId: '', // VLAN ID
  vlanTemplateId24G1: '', // 2.4G第一个SSID的VLAN模板ID
  vlanId24G1: '', // 2.4G第一个SSID的VLAN ID
  vlanTemplateId24G2: '', // 2.4G第二个SSID的VLAN模板ID
  vlanId24G2: '', // 2.4G第二个SSID的VLAN ID
  vlanTemplateId5G1: '', // 5G第一个SSID的VLAN模板ID
  vlanId5G1: '', // 5G第一个SSID的VLAN ID
  vlanTemplateId5G2: '', // 5G第二个SSID的VLAN模板ID
  vlanId5G2: '', // 5G第二个SSID的VLAN ID
  // 以下是暂不使用的变量
  networkLimit: undefined, // 限速
  upstreamLimit: '', // 上行限制
  downstreamLimit: '', // 下行限制
  wifiMaxsta_2G: '',
  wifiMaxsta_5G: '',
  load_balance_interval: '', // SSID负载均衡间隔
  weak_signal_threshold: '', // 信号阙值
  ignore_weak_signal: '', // 忽略弱信号STA
  ignore_excessive_retransmission: '', // 忽略重传过多STA
})

const handleCountryChange = (type: '2G' | '5G', value: string) => {
  if (type === '2G')
    templateForm.wifiChannel_2G = 'auto'

  else
    templateForm.wifiChannel_5G = 'auto'
}

const validAllForm = async () => {
  for (let i = 0; i < formRef.length; i++) {
    const validator = formRef[i]
    const valid = await validator.value?.validate()

    console.log('valid', valid)
    if (!valid?.valid) {
      currentTab.value = i

      return false
    }
  }

  return true
}

// 计算VLAN模板数量
const calculateVlanNum = () => {
  const vlanIds = new Set()

  // 收集所有使用的VLAN模板ID，空值不参与计算
  if (templateForm.vlanTemplateId24G1 && templateForm.vlanTemplateId24G1 !== '')
    vlanIds.add(templateForm.vlanTemplateId24G1)
  if (templateForm.vlanTemplateId24G2 && templateForm.vlanTemplateId24G2 !== '')
    vlanIds.add(templateForm.vlanTemplateId24G2)
  if (templateForm.vlanTemplateId5G1 && templateForm.vlanTemplateId5G1 !== '')
    vlanIds.add(templateForm.vlanTemplateId5G1)
  if (templateForm.vlanTemplateId5G2 && templateForm.vlanTemplateId5G2 !== '')
    vlanIds.add(templateForm.vlanTemplateId5G2)
  if (templateForm.vlanTemplateId && templateForm.vlanTemplateId !== '')
    vlanIds.add(templateForm.vlanTemplateId)

  templateForm.vlanNum = vlanIds.size
}

const show2GPassword = ref(false)
const show5GPassword = ref(false)

const save = async () => {
  if (!(await validAllForm()))
    return

  // 先执行VLAN计算
  calculateVlanNum()

  // 等待VLAN计算完成后再进行数据提取和提交
  await nextTick()

  let {
    id,
    tpl_name,
    tpl_bands,
    model,
    ssid_count,
    description,
    ssid_2g,
    ssid_5g,
    key_2g,
    key_5g,
    ssid_type,
    ssidConfigType,
    net_type,
    ap_lan_ip,
    ap_lan_mask,
    wifiOnOff_2G,
    wifiOnOff_5G,
    wifiApIsolate_2G,
    wifiApIsolate_5G,
    wifiEnable_2G,
    wifiEnable_5G,
    wifiCountry_2G,
    wifiCountry_5G,
    wifiChannel_2G,
    wifiChannel_5G,
    wifiHwMode_2G,
    wifiHwMode_5G,
    wifiHtMode_2G,
    wifiForce40MHzMode_2G,
    wifiHtMode_5G,
    wifiTxpower_2G,
    wifiTxpower_5G,
    wifi80211r_2G,
    wifi80211r_5G,
    wifiAuthmode_2G,
    wifiAuthmode_5G,
    wifiWpa3_2G,
    wifiWpa3_5G,
    ssid,
    encryptionType,
    password,
    isolate,
    quickRoaming,
    roamingProtocol,
    wifiMaxsta_2G,
    wifiMaxsta_5G,

    // 多SSID配置字段
    ssid_2g2,
    key_2g2,

    wifiMaxsta_2G2,
    wifiApIsolate_2G2,
    ssid_5g2,
    key_5g2,

    wifiMaxsta_5G2,
    wifiApIsolate_5G2,
    vlanNum,
    vlanId,
    vlanId24G1,
    vlanId24G2,
    vlanId5G1,
    vlanId5G2,
    vlanTemplateId,
    vlanTemplateId24G1,
    vlanTemplateId24G2,
    vlanTemplateId5G1,
    vlanTemplateId5G2,
    load_balance_interval,
    weak_signal_threshold,
    ignore_weak_signal,
    ignore_excessive_retransmission,
  } = templateForm
  const vlan = vlanList.value.find(v => v.id === vlanTemplateId)
  const vlan24G1 = vlanList.value.find(v => v.id === vlanTemplateId24G1)
  const vlan24G2 = vlanList.value.find(v => v.id === vlanTemplateId24G2)
  const vlan5G1 = vlanList.value.find(v => v.id === vlanTemplateId5G1)
  const vlan5G2 = vlanList.value.find(v => v.id === vlanTemplateId5G2)

  vlanId = vlan?.vlanId || ''
  vlanId24G1 = vlan24G1?.vlanId || ''
  vlanId24G2 = vlan24G2?.vlanId || ''
  vlanId5G1 = vlan5G1?.vlanId || ''
  vlanId5G2 = vlan5G2?.vlanId || ''

  // 处理默认配置的双频合一
  if (ssidConfigType === '0' && ssid_type == '1') {
    ssid_2g = ssid
    ssid_5g = ssid
    key_2g = password
    key_5g = password
    wifiApIsolate_2G = isolate ? '1' : '0'
    wifiApIsolate_5G = isolate ? '1' : '0'
    wifiAuthmode_2G = encryptionType
    wifiAuthmode_5G = encryptionType
    tpl_bands = '2.4G/5G'
  }
  else {
    // 计算频段信息
    const bands = []
    if (wifiOnOff_2G == '0')
      bands.push('2.4G')
    if (wifiOnOff_5G == '0')
      bands.push('5G')
    tpl_bands = bands.length > 0 ? bands.join('/') : '-'
  }

  // 处理多SSID配置的SSID数量
  if (ssidConfigType === '1') {
    let count = 0
    if (wifiOnOff_2G == '0')
      count += ssid2gCount.value
    if (wifiOnOff_5G == '0')
      count += ssid5gCount.value
    ssid_count = count.toString()
  }
  else {
    ssid_count = '1'
  }

  if (wifiAuthmode_2G === 'psk2')
    wifiWpa3_2G = '1'
  else
    wifiWpa3_2G = '0'

  if (wifiAuthmode_5G === 'psk2')
    wifiWpa3_5G = '1'
  else
    wifiWpa3_5G = '0'

  if (wifiAuthmode_2G == 'none')
    key_2g = ''

  if (wifiAuthmode_5G == 'none')
    key_5g = ''

  let postData = {}
  if (ssidConfigType === '1') {
    if (id) {
    }
    else {
      postData = {
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId: vlanId || '',
          vlanId24G1: '',
          vlanId24G2: '',
          vlanId5G1: '',
          vlanId5G2: '',
          ssid_2g2: '',
          key_2g2: '',
          wifiMaxsta_2G2: '',
          wifiApIsolate_2G2: '',
          ssid_5g2: '',
          key_5g2: '',
          wifiMaxsta_5G2: '',
          wifiApIsolate_5G2: '',
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
  }
  else {
    if (id) {
    }
    else {
      postData = {
        name: tpl_name,
        model,
        remark: description,
        vlanId: vlanTemplateId || '',
        vlanId24G1: vlanTemplateId24G1, // 2.4G1 VLAN模板ID
        vlanId24G2: vlanTemplateId24G2, // 2.4G2 VLAN模板ID
        vlanId5G1: vlanTemplateId5G1, // 5G1 VLAN模板ID
        vlanId5G2: vlanTemplateId5G2, // 5G2 VLAN模板ID
        vlanNum,
        ssidConfigType,
        config: {
          tpl_bands,
          ssid_count,
          ssid_2g,
          ssid_5g,
          key_2g,
          key_5g,
          ssid_type,
          net_type,
          ap_lan_ip,
          ap_lan_mask,
          wifiOnOff_2G,
          wifiOnOff_5G,
          wifiApIsolate_2G,
          wifiApIsolate_5G,
          wifiEnable_2G,
          wifiEnable_5G,
          wifiCountry_2G,
          wifiCountry_5G,
          wifiChannel_2G,
          wifiChannel_5G,
          wifiHwMode_2G,
          wifiHwMode_5G,
          wifiHtMode_2G,
          wifiForce40MHzMode_2G,
          wifiHtMode_5G,
          wifiTxpower_2G,
          wifiTxpower_5G,
          wifi80211r_2G,
          wifi80211r_5G,
          wifiAuthmode_2G,
          wifiAuthmode_5G,
          wifiWpa3_2G,
          wifiWpa3_5G,
          wifiMaxsta_2G,
          wifiMaxsta_5G,
          vlanId,
          vlanId24G1,
          vlanId24G2,
          vlanId5G1,
          vlanId5G2,
          ssid_2g2,
          key_2g2,
          wifiMaxsta_2G2,
          wifiApIsolate_2G2,
          ssid_5g2,
          key_5g2,
          wifiMaxsta_5G2,
          wifiApIsolate_5G2,
          vlanTemplateId,
          vlanTemplateId24G1,
          vlanTemplateId24G2,
          vlanTemplateId5G1,
          vlanTemplateId5G2,
          load_balance_interval,
          weak_signal_threshold,
          ignore_weak_signal,
          ignore_excessive_retransmission,
        },
      }
    }
  }
  console.log(postData)

  // 使用新的postData结构进行提交
  $post('/v1/apTemplate', postData).then(res => {
    if (res.msg === 'success') {
      ElMessage.success(t('Config.Mode.SaveSuccess'))
      createTemplateDialog.value = false
      resetForm()
      resetList()
    }
    else {
      ElMessage.error(t('Config.Mode.SaveFailed'))
    }
  })
}

const resetList = () => {
  page.value = 1
  getDeviceList()
}

// 新建模版
const createTemplateDialog = ref(false)

// AC模板对话框
const createACTemplateDialog = ref(false)

// 0-添加 1-编辑
const dialogStatus = ref(0)

const createNewTemplate = () => {
  if (actionType.value === '0') {
    // AP模板：在当前页面创建
    dialogStatus.value = 0 // 新建状态
    openDialog()
  }
  else {
    // AC模板：在当前页面创建
    dialogStatus.value = 0 // 新建状态
    openACDialog(0)
  }
}

const createNewTemplateA = () => {
  distributeTemplateDialog.value = false

  if (dType.value === 0) {
    // AP模板：在当前页面创建
    dialogStatus.value = 0 // 新建状态
    openDialog()
  }
  else {
    // AC模板：在当前页面创建
    dialogStatus.value = 0 // 新建状态
    openACDialog(0)
  }
}

// AC模板相关函数
const openACDialog = (num: number) => {
  if (num === 0)
    resetACForm()

  createACTemplateDialog.value = true
  currentACTab.value = 0
}

// AC模板重置函数
const resetACForm = () => {
  dialogStatus.value = 0 // 重置为新建状态
  acTemplateForm.name = ''
  acTemplateForm.model = acModelList.value && acModelList.value.length > 0 ? String(acModelList.value[0].value) : ''
  acTemplateForm.remark = ''
  acTemplateForm.networkConfig = {
    networkType: 0, // 默认DHCP
    mtu: '1500', // 默认MTU值
    wanUsername: '',
    wanPassword: '',
    wanIpAddress: '',
    wanGateway: '',
    wanNetmask: '',
    wanDns: ['', ''],
  }
  acTemplateForm.lanConfig = {
    lanIpAddress: '',
    lanNetmask: '',
    vlanEnabled: '0',
    vlanTemplateId: '',
    dhcpDisabled: '1',
    dhcpStartValue: '',
    dhcpMaxNumber: '',
  }
  acTemplateForm.systemConfig = {
    password: '',
    timeZone: 'Asia/Shanghai', // 默认时区，参考系统配置
    hostName: '',
  }
}

const nextStepAC = () => {
  acFormRef[currentACTab.value].value?.validate().then((res: any) => {
    if (res.valid)
      currentACTab.value += 1
  })
}

// AC模板保存函数
const saveACTemplate = async () => {
  // 验证所有表单
  for (let i = 0; i < acFormRef.length; i++) {
    const validator = acFormRef[i]
    const valid = await validator.value?.validate()

    if (!valid?.valid) {
      currentACTab.value = i

      return false
    }
  }

  const { lanConfig } = acTemplateForm

  const postData = {
    name: acTemplateForm.name,
    model: acTemplateForm.model,
    remark: acTemplateForm.remark,
    vlanId: lanConfig.vlanTemplateId,
    config: {
      wan: acTemplateForm.networkConfig,
      lan: {
        lanIpAddress: lanConfig.lanIpAddress, // LAN IP地址
        lanNetmask: lanConfig.lanNetmask, // LAN子网掩码
        vlanEnabled: lanConfig.vlanEnabled, // VLAN管理：0-禁用，1-启用
        vlanTemplateId: lanConfig.vlanTemplateId, // VLAN模板ID
      },
      dhcp: {
        dhcpDisabled: lanConfig.dhcpDisabled, // DHCP服务：0-启用，1-禁用
        dhcpStartValue: lanConfig.dhcpStartValue, // DHCP起始值
        dhcpMaxNumber: lanConfig.dhcpMaxNumber, // DHCP最大数量
      },
      system: acTemplateForm.systemConfig,
    },
  }

  try {
    const res = await $post('/v1/acTemplate', postData)

    if (res.msg === 'success') {
      ElMessage.success(t('Config.Mode.SaveSuccess'))
      createACTemplateDialog.value = false
      resetACForm()

      // 刷新模板列表（如果需要的话）
    }
    else {
      ElMessage.error(t('Config.Mode.SaveFailed'))
    }
  }
  catch (error) {
    ElMessage.error(t('Config.Mode.SaveFailed'))
  }
}

const openDialog = () => {
  resetForm()
  createTemplateDialog.value = true
  currentTab.value = 0
}

const resetForm = () => {
  // 重置SSID计数
  ssid2gCount.value = 1
  ssid5gCount.value = 1

  // 重置表单数据
  Object.assign(templateForm, {
    id: '', // （随机生成32位以内可含英文或者数字或下划线的字符串，传值），模板的唯一识别码,id不可变，通过id绑定模板
    tpl_name: '', // 模板名称，支持中文（UTF-8）一个中文占三字节
    model: modelList.value.length > 0 ? String(modelList.value[0].value) : '', // AP型号，默认选中第一个
    tpl_bands: '', // 适用频段
    ssid_count: '', // SSID数量
    modified_at: '', //	最后修改时间
    description: '', //	备注，支持中文（UTF-8）一个中文占三字节
    ssid_2g: '', //	2G SSID
    ssid_5g: '', //	5G SSID
    key_2g: '', //	2G密码
    key_5g: '', //	5G密码
    ssid_type: '0', //	1：SSID双频合一 0：SSID分开
    ssidConfigType: '0', // 0：默认配置 1：多SSID配置
    net_type: '1', //	0：路由模式 1：AP模式，默认AP模式
    ap_lan_ip: '', //	LAN IP
    ap_lan_mask: '', //	LAN子网掩码
    wifiOnOff_2G: '0', // 2.4GWiFi开关 0：开启 1：关闭
    wifiOnOff_5G: '0', // 5.8GWiFi开关 0：开启 1：关闭
    wifiApIsolate_2G: '0', //	2.4G AP隔离  0：关闭 1：开启
    wifiApIsolate_5G: '0', //	5.8G AP隔离  0：关闭 1：开启
    wifiEnable_2G: '0', //	2.4G SSID 隐藏  0：关闭 1：开启
    wifiEnable_5G: '0', //	5.8G SSID 隐藏  0：关闭 1：开启
    wifiCountry_2G: 'CN', //	2.4G 国家代码例如中国传值CN
    wifiCountry_5G: 'CN', //	5.8G 国家代码
    wifiChannel_2G: 'auto', //	2.4G 信道
    wifiChannel_5G: 'auto', //	5.8G 信道
    wifiHwMode_2G: '11axg', //	2.4G协议，默认802.11ax
    wifiHwMode_5G: '11axa', //	5.8G协议，默认802.11ax
    wifiHtMode_2G: 'HT20', //	2.4G带宽
    wifiForce40MHzMode_2G: '0', //	2.4G强制带宽40M选项
    wifiHtMode_5G: 'HT160', //	5.8G带宽
    wifiTxpower_2G: '', // 2.4G信号调节
    wifiTxpower_5G: '', // 5.8G信号调节
    wifi80211r_2G: '', //	2.4G快速漫游r协议
    wifi80211r_5G: '', //	5.8G快速漫游r协议
    wifiAuthmode_2G: 'mixed-psk', //	2.4G加密方式选项，默认wpa1/wpa2
    wifiAuthmode_5G: 'mixed-psk', //	5.8G加密方式选项，默认wpa1/wpa2
    wifiWpa3_2G: '', //	2.4G wpa3 开启标志
    wifiWpa3_5G: '', //	5.8G wpa3 开启标志
    // 多SSID配置字段
    ssid_2g2: '', // 2.4G第二个SSID
    key_2g2: '', // 2.4G第二个密码
    wifiMaxsta_2G2: '', // 2.4G第二个最大连接数
    wifiApIsolate_2G2: '0', // 2.4G第二个AP隔离
    ssid_5g2: '', // 5G第二个SSID
    key_5g2: '', // 5G第二个密码
    wifiMaxsta_5G2: '', // 5G第二个最大连接数
    wifiApIsolate_5G2: '0', // 5G第二个AP隔离
    // VLAN配置字段
    vlanTemplateId: '', // VLAN模板ID
    vlanId: '', // VLAN ID
    vlanTemplateId24G1: '', // 2.4G第一个SSID的VLAN模板ID
    vlanId24G1: '', // 2.4G第一个SSID的VLAN ID
    vlanTemplateId24G2: '', // 2.4G第二个SSID的VLAN模板ID
    vlanId24G2: '', // 2.4G第二个SSID的VLAN ID
    vlanTemplateId5G1: '', // 5G第一个SSID的VLAN模板ID
    vlanId5G1: '', // 5G第一个SSID的VLAN ID
    vlanTemplateId5G2: '', // 5G第二个SSID的VLAN模板ID
    vlanId5G2: '', // 5G第二个SSID的VLAN ID
    // 双频合一的参数
    ssid: '', // SSID名称
    encryptionType: 'mixed-psk', // 加密类型，默认wpa1/wpa2
    password: '', // 密码
    isolate: false, // 隔离
    // 漫游配置
    quickRoaming: false, // 快速漫游
    roamingProtocol: undefined, // 漫游协议
    // 以下是暂不使用的变量
    networkLimit: undefined, // 限速
    upstreamLimit: '', // 上行限制
    downstreamLimit: '', // 下行限制
    load_balance_interval: '', // SSID负载均衡间隔
    weak_signal_threshold: '', // 信号阙值
    ignore_weak_signal: '', // 忽略弱信号STA
    ignore_excessive_retransmission: '', // 忽略重传过多STA
  })
}

// 获取AC型号列表
const acModelList = ref()

const getAcList = () => {
  $get('/v1/acModel', { }).then(res => {
    if (res.msg === 'success') {
      console.log('AC API响应:', res)

      const models = res.result || []

      console.log('提取的AC型号:', Array.from(models))

      acModelList.value = [
        ...Array.from(models).map(item => ({
          label: String(item),
          value: String(item),
        })),
      ]

      console.log('acModelList:', acModelList.value)
    }
  }).catch(err => {
    console.error('获取AC列表失败:', err)
  })
}

onMounted(() => {
  getDeviceList()
  getVlanList()
  getAcList()
  getTimezoneList()

  // 启动时间更新
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)

  if (route.query.templateId)
    templateId = route.query.templateId as string
})

onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
})

// 组件卸载时清理
onBeforeUnmount(() => {
  startTime.value = 0
})

const channelOptions2g = computed(() => {
  const index = COUNTRY_OPTIONS_LOCALIZED.value.findIndex((data: any) => data.value === templateForm.wifiCountry_2G)

  return CHANNEL_ARR_2G[index] || []
})

const channelOptions5g = computed(() => {
  const index = COUNTRY_OPTIONS_LOCALIZED.value.findIndex((data: any) => data.value === templateForm.wifiCountry_5G)

  return CHANNEL_ARR_5G[index] || []
})

const roaming_protocol = [
  {
    label: '802.11r',
    value: 0,
  },
]

const selectedBandwidth = ref('20M')

watch(selectedBandwidth, newVal => {
  switch (newVal) {
    case '40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '1'
      break
    case '20/40M':
      templateForm.wifiHtMode_2G = 'HT40'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
    case '20M':
    default:
      templateForm.wifiHtMode_2G = 'HT20'
      templateForm.wifiForce40MHzMode_2G = '0'
      break
  }
}, { immediate: true })

const validatePassword = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.encryptionType === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEightMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_2G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5G = (value: string) => {
  // 默认配置且双频合一时不需要验证分开的密码
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

const validatePasswordEight5GMore = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (templateForm.wifiAuthmode_5G === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 新增：根据5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = templateForm.wifiChannel_5G

  console.log('channel', channel)

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = templateForm.wifiHtMode_5G
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    templateForm.wifiHtMode_5G = newBandWidthOptions[0].value
  }
}

const requiredValidatorNew = (value: unknown, message: string) => {
  // 默认配置且双频合一时不需要验证分开的SSID
  if (templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1' || templateForm.ssidConfigType === '1')
    return true

  if (isNullOrUndefined(value) || isEmptyArray(value) || value === false)
    return 'This field is required'

  return !!String(value).trim().length || message || 'This field is required'
}

// VLAN列表数据
const vlanList = ref<any[]>([])

// VLAN选择处理函数
const handleVlanSelect = (field: string, vlanId: string) => {
}

// 获取VLAN列表
const getVlanList = () => {
  $get('/v1/vlanConfigs', { page: 1, size: 1000 }).then((res: any) => {
    if (res.msg === 'success' || res.msg === 'success') {
      // 兼容不同后端返回结构
      const rows = res.result?.rows || []

      vlanList.value = [
        { id: '', vlanId: '' },
        ...rows,
      ]

      vlanList.value.forEach(item => {
        if (item.id)
          item.itemTitle = `${item.name} VLAN ${item.vlanId}`

        else
          item.itemTitle = t('PleaseSelect')
      })
    }
    else {
      ElMessage.error(res.err_message || res.msg || '获取VLAN列表失败')
    }
  })
}

const dType = ref(0)

const dTypeOptions = [
  { label: t('APDistribute'), value: 0 },
  { label: t('ACDistribute'), value: 1 },
]

// 新建模板类型选择
const actionTypeList = computed(() => [
  { label: t('Config.Mode.NewAPTemplate'), value: '0' },
  { label: t('Config.Mode.NewACTemplate'), value: '1' },
])

const actionType = ref('0')

// 网络配置相关常量
const networkList = [
  {
    label: t('NetworkConfig.Network.DynamicIP'),
    value: 0,
  },
  {
    label: t('NetworkConfig.Network.PPPoE'),
    value: 1,
  },
  {
    label: t('NetworkConfig.Network.StaticIP'),
    value: 2,
  },
]

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { label: '*********/8', value: '*********' },
  { label: '***********/9', value: '***********' },
  { label: '***********/10', value: '***********' },
  { label: '***********/11', value: '***********' },
  { label: '***********/12', value: '***********' },
  { label: '***********/13', value: '***********' },
  { label: '***********/14', value: '***********' },
  { label: '***********/15', value: '***********' },

  // B类网络常用
  { label: '***********/16', value: '***********' },
  { label: '*************/17', value: '*************' },
  { label: '*************/18', value: '*************' },
  { label: '*************/19', value: '*************' },
  { label: '*************/20', value: '*************' },
  { label: '*************/21', value: '*************' },
  { label: '*************/22', value: '*************' },
  { label: '255.255.254.0/23', value: '255.255.254.0' },

  // C类网络常用
  { label: '255.255.255.0/24', value: '255.255.255.0' },
  { label: '255.255.255.128/25', value: '255.255.255.128' },
  { label: '255.255.255.192/26', value: '255.255.255.192' },
  { label: '255.255.255.224/27', value: '255.255.255.224' },
  { label: '255.255.255.240/28', value: '255.255.255.240' },
  { label: '255.255.255.248/29', value: '255.255.255.248' },
  { label: '255.255.255.252/30', value: '255.255.255.252' },

  // 特殊用途
  { label: '255.255.255.254/31', value: '255.255.255.254' },
  { label: '255.255.255.255/32', value: '255.255.255.255' },
]

// 时区列表
const timezoneList = ref([])

// 获取时区列表
const getTimezoneList = () => {
  $get('/v1/timezone', {}).then(res => {
    console.log(res)
    if (res.msg == 'success') {
      const zoneNameArray = JSON.parse(res.result)

      timezoneList.value = zoneNameArray.map((item: any) => {
        return {
          label: item,
          value: item,
        }
      })
    }
  })
}

// AC模板相关变量
const currentACTab = ref(0)

const acTabList = ref([
  { label: t('Config.Mode.BasicSettings'), value: 0 },
  { label: t('Config.AP.NetworkSettings'), value: 1 },
  { label: t('Config.AP.LANSettings'), value: 2 },
  { label: t('Config.AP.SystemConfig'), value: 3 },
])

// AC模板表单引用
const acFormRef1 = ref()
const acFormRef2 = ref()
const acFormRef3 = ref()
const acFormRef4 = ref()
const acFormRef = [acFormRef1, acFormRef2, acFormRef3, acFormRef4]

// AC模板表单数据
const acTemplateForm = reactive({
  name: '', // 模板名称
  model: '', // AC型号
  remark: '', // 备注
  // 网络设置
  networkConfig: {
    networkType: 0, // 网络类型：0-DHCP, 1-PPPoE, 2-静态IP
    mtu: '1500', // MTU值，所有模式都可填
    // PPPoE 模式字段
    wanUsername: '', // PPPoE用户名
    wanPassword: '', // PPPoE密码
    // 静态IP 模式字段
    wanIpAddress: '', // IP地址
    wanGateway: '', // 网关
    wanNetmask: '', // 子网掩码
    wanDns: ['', ''], // DNS服务器数组，两个组
  },

  // LAN设置
  lanConfig: {
    lanIpAddress: '', // LAN IP地址
    lanNetmask: '', // LAN子网掩码
    vlanEnabled: '0', // VLAN管理：0-禁用，1-启用
    vlanTemplateId: '', // VLAN模板ID
    dhcpDisabled: '1', // DHCP服务：0-启用，1-禁用
    dhcpStartValue: '', // DHCP起始值
    dhcpMaxNumber: '', // DHCP最大数量
  },

  // 系统配置
  systemConfig: {
    password: '', // 管理员密码
    timeZone: 'Asia/Shanghai', // 时区，默认值参考系统配置
    hostName: '', // 设备名称
  },
})

watch(dType, () => {
  selectedModel.value = null // 切换设备类型时清空型号选择
  getDeviceList()
})

// 验证函数
const validateAccountAndPassword = (value: string) => {
  // 账号密码验证：允许字母、数字、下划线、点、@符号，长度1-64
  const regex = /^[\w.@]{1,64}$/

  return regex.test(value)
}

const isValidIPv4 = (ip: string) => {
  const regex = /^((25[0-5]|2[0-4]\d|[01]?\d{1,2})\.){3}(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/

  return regex.test(ip)
}

// LAN IP验证器
const lanIpValidator = (value: any) => {
  if (!value)
    return t('NetworkConfig.LAN.Required')

  if (!isValidIPv4(value))
    return t('NetworkConfig.LAN.InvalidIP')

  return true
}

// 子网掩码验证器
const subnetMaskValidator = (value: any) => {
  if (!value)
    return t('NetworkConfig.LAN.Required')

  return true
}

// 计算子网掩码中的主机位数
const calculateHostBits = (subnetMask: string) => {
  if (!subnetMask)
    return 0

  const parts = subnetMask.split('.')
  if (parts.length !== 4)
    return 0

  let binarySubnetMask = ''
  for (const part of parts) {
    const num = Number.parseInt(part, 10)
    if (isNaN(num) || num < 0 || num > 255)
      return 0
    binarySubnetMask += num.toString(2).padStart(8, '0')
  }

  return binarySubnetMask.length - binarySubnetMask.indexOf('0')
}

const maxAddressesLimit = computed(() => {
  // 计算子网掩码中的主机位数
  const hostBits = calculateHostBits(acTemplateForm.lanConfig.lanNetmask)

  return 2 ** hostBits - 2
})

const maxAddresses = computed(() => {
  // 计算子网掩码中的主机位数
  const hostBits = calculateHostBits(acTemplateForm.lanConfig.lanNetmask)

  // 计算最大数量的限额  // 计算最大可用主机数
  const maxHosts = 2 ** hostBits - 2

  console.log('最大可用主机数:', maxHosts)

  let maxNum = maxHosts - (Number(acTemplateForm.lanConfig.dhcpStartValue) - 1)
  if (maxNum < 1)
    maxNum = 1

  return maxNum
})

// DHCP起始值验证器 - 参考系统LAN配置，增强版本
const dhcpValidator = (value: any) => {
  const numValue = Number(value)

  // 如果DHCP禁用，直接返回true
  if (acTemplateForm.lanConfig.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 数值有效性和最大值校验
  if (!Number.isNaN(numValue) && numValue >= maxAddressesLimit.value)
    return `${t('NetworkConfig.LAN.MaxValueExceeded')}${maxAddressesLimit.value}`

  return true
}

// DHCP最大数量验证器 - 参考系统LAN配置，增强版本
const dhcpMaxValidator = (value: any) => {
  // 将value转换为数字进行比较
  const numValue = Number(value)

  console.log(numValue, maxAddresses.value)

  // 如果DHCP禁用，直接返回true
  if (acTemplateForm.lanConfig.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 数值有效性和最大值校验
  if (!Number.isNaN(numValue) && numValue >= maxAddresses.value)
    return `${t('NetworkConfig.LAN.MaxValueExceededA')}${maxAddresses.value - 1}${t('NetworkConfig.LAN.MaxValueExceededB')}`

  return true
}

// VLAN配置验证器
const vlanValidator = (value: any) => {
  // 如果VLAN管理禁用，直接返回true
  if (acTemplateForm.lanConfig.vlanEnabled === '0')
    return true

  // 空值校验
  if (!value)
    return t('Config.Mode.SelectVLAN')

  return true
}

// 防止小数点输入
const preventDecimal = (event: KeyboardEvent) => {
  if (event.key === '.' || event.key === ',')
    event.preventDefault()
}

// 处理整数输入
const handleIntegerInput = (event: any, field: string) => {
  const value = event.target.value

  // 移除非数字字符
  const numericValue = value.replace(/\D/g, '')

  // 更新对应字段
  if (field === 'dhcpStartValue')
    acTemplateForm.lanConfig.dhcpStartValue = numericValue
  else if (field === 'dhcpMaxNumber')
    acTemplateForm.lanConfig.dhcpMaxNumber = numericValue
}

// 当前时间显示
const currentTime = ref('')
const currentTimezone = ref('')

// 密码显示控制
const showAdminPassword = ref(false)

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  const timezone = acTemplateForm.systemConfig.timeZone || 'Asia/Shanghai'

  // 格式化时间为 2025/5/14 11:49:52
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const seconds = now.getSeconds()

  currentTime.value = `${year}/${month}/${day} ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  currentTimezone.value = timezone
}

// 时间更新定时器
let timeInterval: NodeJS.Timeout | null = null

// 监听时区变化，更新时间显示
watch(() => acTemplateForm.systemConfig.timeZone, () => {
  updateCurrentTime()
})
</script>

<template>
  <div>
    <!-- 新建模板 -->
    <VNavigationDrawer
      v-if="createTemplateDialog"
      v-model="createTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ dialogStatus === 1 ? t('Config.Mode.EditTemplate') : t('Config.Mode.NewConfigTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="createTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 - flex-grow-1确保占据剩余空间 -->
        <div class="flex-grow-1 d-flex flex-column overflow-hidden">
          <!-- 标签页固定 -->
          <div class="flex-shrink-0 px-6 pt-4">
            <VTabs
              v-model="currentTab"
              class="mb-4"
              align-tabs="center"
              fixed-tabs
            >
              <VTab
                v-for="(item, index) in tabList"
                :key="index"
                :value="item.value"
              >
                {{ item.label }}
              </VTab>
            </VTabs>
          </div>

          <!-- 内容区可滚动 -->
          <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
            <VWindow v-model="currentTab">
              <!-- 选择型号 -->
              <VWindowItem :value="0">
                <VForm ref="formRef1">
                  <!-- 模板名称输入框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TemplateName') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.tpl_name"
                      :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterTemplateName')), (v: string) => {
                        if (!isByteLengthInRange(v, 1, 64)) {
                          return t('Config.Mode.TemplateNameLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.EnterTemplateName')"
                    />
                  </div>
                  <!-- 备注输入框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Remark') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.description"
                      :rules="[(v: string) => {
                        if (!isByteLengthInRange(v, 0, 128)) {
                          return t('Config.Mode.RemarkLengthError')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectRemark')"
                    />
                  </div>
                  <!-- AP型号选择下拉框 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.SelectModel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.model"
                      class="mb-4"
                      :placeholder="t('Config.Mode.SelectAPModel')"
                      :items="modelList"
                      item-title="label"
                      item-value="value"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <!-- 基础配置 -->
              <VWindowItem :value="1">
                <VForm ref="formRef2">
                  <!-- SSID配置类型选择按钮组 -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <BtnGroupSelector
                      v-model:value="templateForm.ssidConfigType"
                      fill-row
                      :options="ssidConfigTypeList"
                      @update:value="ssidConfigTypeChange"
                    />
                  </div>

                  <!-- 双频合一开关 (仅默认配置显示) -->
                  <div
                    v-if="templateForm.ssidConfigType === '0'"
                    class="d-flex justify-space-between align-center mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90">
                      {{ t('Config.AP.DualBandUnify') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.ssid_type"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                        @update:model-value="ssidTypeChange"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ t('Config.AP.DualBandUnifyHint') }}
                      </span>
                    </div>
                  </div>
                  <!-- 默认配置 - 双频合一 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '1'">
                    <!-- 双频合一SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 双频合一加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.encryptionType"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                        item-title="label"
                        item-value="value"
                      />
                    </div>
                    <!-- 双频合一密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.password"
                        :append-inner-icon="showMergePassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePassword]"
                        :type="showMergePassword ? 'text' : 'password'"
                        @click:append-inner="
                          showMergePassword = !showMergePassword
                        "
                      />
                    </div>
                    <!-- 双频合一客户端隔离开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Isolate') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.isolate"
                          class="mr-2"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{ templateForm.isolate ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <VDivider class="mb-4" />
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-primary text-h5">
                      {{ t('Config.Mode.WirelessSettings2G') }}
                    </div>
                    <VBtn
                      v-if="templateForm.ssidConfigType === '1' && ssid2gCount < 2"
                      color="primary"
                      size="small"
                      variant="outlined"
                      @click="addSSID2G"
                    >
                      {{ t('Config.Mode.AddSSID') }}
                    </VBtn>
                  </div>

                  <!-- 默认配置 - 2.4G分开模式 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                    <!-- 启用Wi-Fi开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_2G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                    <!-- SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_2g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 2.4G加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_2G"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.SelectEncryption'))]"
                      />
                    </div>
                    <!-- 2.4G密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_2g"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        :rules="[validatePasswordEight]"
                        :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :type="show2GPassword ? 'text' : 'password'"
                        @click:append-inner="show2GPassword = !show2GPassword"
                      />
                    </div>
                  </div>

                  <!-- 多SSID配置 - 2.4G WiFi开关 -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 2.4G WiFi总开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_2G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_2G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                  </div>

                  <!-- 通用2.4G设置 -->
                  <!-- 2.4G协议选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_2G"
                      :items="PROTOCOL_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectProtocol')"
                    />
                  </div>
                  <!-- 2.4G国家码选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiCountry_2G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectCountry')"
                      @update:model-value="() => {
                        templateForm.wifiChannel_2G = 'auto'
                      }"
                    />
                  </div>
                  <!-- 2.4G信道选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_2G"
                      :items="channelOptions2g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectChannel')"
                    />
                  </div>
                  <!-- 2.4G信道带宽选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="selectedBandwidth"
                      :items="BAND_WIDTH_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectBandwidth')"
                    />
                  </div>
                  <!-- 2.4G发射功率选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_2G"
                      :items="TX_POWER_2G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.Mode.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectTxPower')"
                    />
                  </div>
                  <!-- 2.4G最大连接数输入 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField v-model="templateForm.wifiMaxsta_2G" />
                  </div>

                  <!-- 多SSID配置卡片 - 2.4G -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 2.4G SSID 1 -->
                    <VCard
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID2G1') }}</span>
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEightMore]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId24G1"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId24G1', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_2G"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_2G"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>

                    <!-- 2.4G SSID 2 -->
                    <VCard
                      v-if="ssid2gCount >= 2"
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID2G2') }}</span>
                        <VBtn
                          color="error"
                          size="small"
                          variant="text"
                          icon="tabler-trash"
                          @click="removeSSID2G"
                        />
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_2g2"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_2g2"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEightMore]"
                            :append-inner-icon="show2GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show2GPassword ? 'text' : 'password'"
                            @click:append-inner="show2GPassword = !show2GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId24G2"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId24G2', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_2G2"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_2G2"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_2G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>
                  <!-- 2.4G客户端隔离开关（仅默认配置分开模式显示） -->
                  <div
                    v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Isolate') }}
                    </div>
                    <VSwitch
                      v-model="templateForm.wifiApIsolate_2G"
                      false-value="0"
                      true-value="1"
                      :label="templateForm.wifiApIsolate_2G === '0' ? t('Config.Mode.Off') : t('Config.Mode.On')"
                    />
                  </div>
                  <VDivider class="mb-4" />
                  <!-- 5G -->
                  <div class="d-flex justify-space-between align-center mb-4">
                    <div class="text-primary text-h5">
                      {{ t('Config.Mode.WirelessSettings5G') }}
                    </div>
                    <VBtn
                      v-if="templateForm.ssidConfigType === '1' && ssid5gCount < 2"
                      color="primary"
                      size="small"
                      variant="outlined"
                      @click="addSSID5G"
                    >
                      {{ t('Config.Mode.AddSSID') }}
                    </VBtn>
                  </div>

                  <!-- 默认配置 - 5G分开模式 -->
                  <div v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type === '0'">
                    <!-- 5G WiFi开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <div class="d-flex align-center">
                        <VSwitch
                          v-model="templateForm.wifiOnOff_5G"
                          class="mr-2"
                          false-value="1"
                          true-value="0"
                        />
                        <span class="text-subtitle-2 text-on-surface opacity-50">
                          {{
                            templateForm.wifiOnOff_5G === "0" ? t('Config.Mode.On') : t('Config.Mode.Off')
                          }}
                        </span>
                      </div>
                    </div>
                    <!-- 5G SSID名称输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSID') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ssid_5g"
                        :rules="[(v: string) => requiredValidatorNew(v, t('Config.Mode.EnterSSID'))]"
                        :placeholder="t('Config.Mode.EnterSSID')"
                      />
                    </div>
                    <!-- 5G加密类型选择 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EncryptionType') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.wifiAuthmode_5G"
                        :items="ENCRYPTION_TYPE_LOCALIZED"
                        :rules="templateForm.ssid_type === '0' ? [(v: string) => {
                          if (v === null) {
                            return t('Config.Mode.SelectEncryption')
                          }
                        }] : []"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectEncryption')"
                      />
                    </div>
                    <!-- 5G密码输入 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.Password') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.key_5g"
                        :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                        :rules="[validatePasswordEight5G]"
                        :type="show5GPassword ? 'text' : 'password'"
                        :placeholder="t('Config.Mode.EnterPassword')"
                        @click:append-inner="show5GPassword = !show5GPassword"
                      />
                    </div>
                  </div>

                  <!-- 多SSID配置 - 5G WiFi开关 -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 5G WiFi总开关 -->
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.EnableWiFi') }}
                      </div>
                      <VSwitch
                        v-model="templateForm.wifiOnOff_5G"
                        false-value="1"
                        true-value="0"
                        :label="templateForm.wifiOnOff_5G == '0' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                      />
                    </div>
                  </div>

                  <!-- 通用5G设置 -->
                  <!-- 5G协议选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Protocol') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHwMode_5G"
                      :items="PROTOCOL_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectProtocol')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectProtocol')"
                    />
                  </div>
                  <!-- 5G国家码选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Country') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiCountry_5G"
                      :items="COUNTRY_OPTIONS_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectCountry')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectCountry')"
                      @update:model-value="() => {
                        templateForm.wifiChannel_5G = 'auto'
                      }"
                    />
                  </div>
                  <!-- 5G信道选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Channel') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiChannel_5G"
                      :items="channelOptions5g"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectChannel')
                        }
                      }]"
                      :placeholder="t('Config.Mode.SelectChannel')"
                      @update:model-value="changeChannel"
                    />
                  </div>
                  <!-- 5G信道带宽选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Bandwidth') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiHtMode_5G"
                      :items="BAND_WIDTH_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectBandwidth')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectBandwidth')"
                    />
                  </div>
                  <!-- 5G发射功率选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.TxPower') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.wifiTxpower_5G"
                      :items="TX_POWER_5G_LOCALIZED"
                      :rules="[(v: string) => {
                        if (!v && v !== '') {
                          return t('Config.Mode.SelectTxPower')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectTxPower')"
                    />
                  </div>
                  <!-- 5G最大连接数输入 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.MaxConnections') }}
                    </div>
                    <AppTextField v-model="templateForm.wifiMaxsta_5G" />
                  </div>

                  <!-- 多SSID配置卡片 - 5G -->
                  <div v-if="templateForm.ssidConfigType === '1'">
                    <!-- 5G SSID 1 -->
                    <VCard
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID5G1') }}</span>
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight5GMore]"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show5GPassword ? 'text' : 'password'"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId5G1"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId5G1', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_5G"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>

                    <!-- 5G SSID 2 -->
                    <VCard
                      v-if="ssid5gCount >= 2"
                      class="mb-4"
                      variant="outlined"
                    >
                      <VCardTitle class="d-flex justify-space-between align-center">
                        <span>{{ t('Config.Mode.SSID5G2') }}</span>
                        <VBtn
                          color="error"
                          size="small"
                          variant="text"
                          icon="tabler-trash"
                          @click="removeSSID5G"
                        />
                      </VCardTitle>
                      <VCardText>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.SSID') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.ssid_5g2"
                            :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterSSID'))]"
                            :placeholder="t('Config.Mode.EnterSSID')"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.Password') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.key_5g2"
                            :placeholder="t('Config.Mode.EnterPassword')"
                            :rules="[validatePasswordEight5GMore]"
                            :append-inner-icon="show5GPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :type="show5GPassword ? 'text' : 'password'"
                            @click:append-inner="show5GPassword = !show5GPassword"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.VLANID') }}
                          </div>
                          <AppSelect
                            v-model="templateForm.vlanTemplateId5G2"
                            :items="vlanList"
                            item-title="itemTitle"
                            item-value="id"
                            :placeholder="t('Config.Mode.SelectVLAN')"
                            @update:model-value="(value: any) => handleVlanSelect('vlanId5G2', value)"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.MaxConnections') }}
                          </div>
                          <AppTextField
                            v-model="templateForm.wifiMaxsta_5G2"
                            :placeholder="t('Config.Mode.EnterMaxConnections')"
                            type="number"
                          />
                        </div>
                        <div class="d-flex justify-space-between align-start mb-4">
                          <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                            {{ t('Config.Mode.APIsolation') }}
                          </div>
                          <VSwitch
                            v-model="templateForm.wifiApIsolate_5G2"
                            false-value="0"
                            true-value="1"
                            :label="templateForm.wifiApIsolate_5G2 === '1' ? t('Config.Mode.On') : t('Config.Mode.Off')"
                          />
                        </div>
                      </VCardText>
                    </VCard>
                  </div>

                  <!-- 5G客户端隔离开关（仅默认配置分开模式显示） -->
                  <div
                    v-if="templateForm.ssidConfigType === '0' && templateForm.ssid_type == '0'"
                    class="d-flex justify-space-between align-start mb-4"
                  >
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.Isolate') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifiApIsolate_5G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{
                          templateForm.wifiApIsolate_5G === "0" ? t('Config.Mode.Off') : t('Config.Mode.On')
                        }}
                      </span>
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="2">
                <VForm ref="formRef3">
                  <!-- 网络类型选择 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.NetworkType') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.net_type"
                      :items="NET_TYPE_LOCALIZED"
                      :rules="[(v: string) => {
                        if (v === null) {
                          return t('Config.Mode.SelectNetworkType')
                        }
                      }]"
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectNetworkType')"
                    />
                  </div>

                  <!-- VLAN配置（仅在网络类型为VLAN时显示） -->
                  <div
                    v-if="templateForm.net_type === '2'"
                    class="mb-4"
                  >
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.VLANID') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.vlanTemplateId"
                        :items="vlanList"
                        item-title="itemTitle"
                        item-value="id"
                        :placeholder="t('Config.Mode.SelectVLAN')"
                        :rules="[(v: string) => {
                          if (!v) {
                            return t('Config.Mode.SelectVLAN')
                          }
                          return true
                        }]"
                        @update:model-value="(value: any) => handleVlanSelect('vlanId', value)"
                      />
                    </div>
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Device.AP.LANIP') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.ap_lan_ip"
                      :placeholder="t('Device.AP.Required')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.LAN.SubnetMask') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.ap_lan_mask"
                      :items="subnetMaskOptions"
                      :placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
                      item-title="label"
                      item-value="value"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.SpeedLimit') }}
                    </div>
                    <AppSelect
                      v-model="templateForm.networkLimit"
                      :items="SPEED_LIMIT_TYPE_LIST"
                      disabled
                      item-title="label"
                      item-value="value"
                      :placeholder="t('Config.Mode.SelectSpeedLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.UpstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.upstreamLimit"
                      disabled
                      :placeholder="t('Config.Mode.EnterUpstreamLimit')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.DownstreamLimit') }}
                    </div>
                    <AppTextField
                      v-model="templateForm.downstreamLimit"
                      disabled
                      :placeholder="t('Config.Mode.EnterDownstreamLimit')"
                    />
                  </div>
                </VForm>
              </VWindowItem>
              <VWindowItem :value="3">
                <VForm ref="formRef4">
                  <!-- 快速漫游开关 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.RoamingProtocol2G') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifi80211r_2G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ templateForm.wifi80211r_2G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                      </span>
                    </div>
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.RoamingProtocol5G') }}
                    </div>
                    <div class="d-flex align-center">
                      <VSwitch
                        v-model="templateForm.wifi80211r_5G"
                        class="mr-2"
                        false-value="0"
                        true-value="1"
                      />
                      <span class="text-subtitle-2 text-on-surface opacity-50">
                        {{ templateForm.wifi80211r_5G === '1' ? t('Config.Mode.On') : t('Config.Mode.Off') }}
                      </span>
                    </div>
                  </div>
                  <!-- 漫游协议选择（仅在快速漫游开启时显示） -->
                  <div v-if="templateForm.wifi80211r_5G === '1' || templateForm.wifi80211r_2G === '1'">
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.RoamingProtocol') }}
                      </div>
                      <AppSelect
                        v-model="templateForm.roamingProtocol"
                        :items="ROAMING_PROTOCOL"
                        item-title="label"
                        item-value="value"
                        :placeholder="t('Config.Mode.SelectRoamingProtocol')"
                        :rules="[(v: number | undefined) => {
                          if (v === undefined) {
                            return t('Config.Mode.SelectRoamingProtocol')
                          }
                          return true
                        }]"
                      />
                    </div>
                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.SSIDLoadBalancing') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.load_balance_interval"
                        class="mb-4"
                        :placeholder="t('Config.Mode.SSIDLoadBalancingPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.DisconnectWeakSignal') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.weak_signal_threshold"
                        class="mb-4"
                        :placeholder="t('Config.Mode.DisconnectWeakSignalPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.IgnoreWeakSignal') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ignore_weak_signal"
                        class="mb-4"
                        :placeholder="t('Config.Mode.IgnoreWeakSignalPlaceholder')"
                      />
                    </div>

                    <div class="d-flex justify-space-between align-start mb-4">
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('Config.Mode.IgnoreExcessiveRetransmission') }}
                      </div>
                      <AppTextField
                        v-model="templateForm.ignore_excessive_retransmission"
                        :placeholder="t('Config.Mode.IgnoreExcessiveRetransmissionPlaceholder')"
                        :rules="[(v: string) => requiredValidator(v, t('Config.Mode.NumericRangeValidation'))]"
                      />
                    </div>
                  </div>
                </VForm>
              </VWindowItem>
            </VWindow>
          </div>
        </div>

        <!-- 底部固定 -->
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-end">
          <VBtn
            color="secondary"
            variant="tonal"
            class="mr-2"
            @click="createTemplateDialog = false"
          >
            {{ t('Config.Mode.Cancel') }}
          </VBtn>
          <VBtn
            v-if="currentTab !== tabList.length - 1"
            color="primary"
            variant="flat"
            @click="nextStep"
          >
            {{ t('Config.Mode.Next') }}
          </VBtn>
          <VBtn
            v-else
            color="primary"
            variant="flat"
            @click="save"
          >
            {{ t('Config.Mode.SaveTemplate') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 下发配置 -->
    <VNavigationDrawer
      v-if="distributeTemplateDialog"
      v-model="distributeTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ dType === 1 ? t('DeployACTemplate') : t('DeployAPTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="distributeTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间内容区 -->
        <div class="flex-grow-1 px-6 py-4 overflow-y-auto">
          <!-- 选择模板 -->
          <div class="mb-6">
            <VForm ref="formRef1">
              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  {{ t('Config.ConfigTemplate') }}
                </div>
                <AppSelect
                  v-model="distributeForm.templateId"
                  :items="templateList"
                  :rules="[(v: string) => {
                    if (v === null) {
                      return t('Config.AP.SelectPreConfigTemplate')
                    }
                  }]"
                  class="mb-4"
                  item-title="name"
                  item-value="id"
                  :placeholder="t('Config.AP.SelectPreConfigTemplate')"
                />
              </div>
            </VForm>
            <div
              class="text-primary text-caption cursor-pointer"
              @click="createNewTemplateA"
            >
              {{ t('Config.AP.CreateNewTemplate') }}
            </div>
          </div>

          <!-- 下发方式 -->
          <div class="mb-6">
            <VForm ref="formRef2">
              <div class="d-flex justify-space-between align-start mb-4">
                <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                  {{ t('Config.DistributionMethod') }}
                </div>
                <AppSelect
                  v-model="distributeForm.distributeType"
                  :items="TEMPLATE_DISTRIBUTION_METHOD_LOCALIZED"
                  :rules="[(v: string) => {
                    if (v === null) {
                      return t('Config.AP.SelectDistributionMethod')
                    }
                  }]"
                  class="mb-4"
                  item-title="label"
                  item-value="value"
                  :placeholder="t('Config.AP.SelectDistributionMethod')"
                />
              </div>
              <VForm ref="formRef2">
                <div
                  v-if="distributeForm.distributeType === 1"
                  class="d-flex justify-space-between align-start mb-4"
                >
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('Config.AP.SelectDistributionTime') }}
                  </div>
                  <AppDateTimePicker
                    v-model="distributeForm.distributeTime"
                    :config="{ enableTime: true, dateFormat: 'Y / m / d  H:i' }"
                    :placeholder="t('Config.AP.YearMonthDayTime')"
                  />
                </div>
              </VForm>
            </vform>
          </div>
          <!-- 目标AP列表吸底显示 -->
          <div
            class="ap-list-footer"
            style="background: #fff; border-block-start: 1px solid #eee;"
          >
            <div class="mb-2 font-weight-medium table-title">
              目标AP（{{ selectedRows.length }}）
            </div>
            <div style="max-block-size: 160px;overflow-y: auto;">
              <div
                v-for="sn in selectedRows"
                :key="sn"
                class="d-flex align-center mb-2 table-item"
              >
                <span class="mr-2 table-name">{{ list.find(item => item.sn === sn)?.name }}</span>
                <span class="mr-2 table-sn">{{ sn }}</span>
                <VSpacer />
                <VChip
                  v-if="list.find(item => item.sn === sn)?.online"
                  label
                  color="success"
                  size="small"
                >
                  {{ t('Online') }}
                </VChip>
                <VChip
                  v-else
                  label
                  color="error"
                  size="small"
                >
                  {{ t('Offline') }}
                </VChip>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部固定 -->
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-end">
          <VBtn
            color="secondary"
            variant="tonal"
            class="mr-2"
            @click="distributeTemplateDialog = false"
          >
            {{ t('Config.AP.Cancel') }}
          </VBtn>
          <VBtn
            :disabled="distributeDisabled"
            color="primary"
            variant="flat"
            @click="distributeHandle"
          >
            {{ t('Config.AP.ConfirmDeploy') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 进度显示对话框 -->
    <VDialog
      v-model="showProgressDialog"
      :width="500"
      persistent
    >
      <VCard>
        <VCardTitle class="text-center">
          {{ t('Config.AP.DeployConfig') }}
        </VCardTitle>
        <VCardText>
          <div class="d-flex flex-column align-center">
            <div class="mb-4 font-weight-medium">
              {{ distributeState === 2 ? t('Config.AP.ConfigDistributionComplete') : t('Config.AP.ConfigDistributing') }}
            </div>
            <div class="progress d-flex align-center mb-4">
              <VProgressLinear
                :model-value="distributeProgress"
                color="primary"
                class="mr-4"
                style="inline-size: 200px;"
              />
              <div>{{ distributeProgress }}%</div>
            </div>
            <div class="distribute-log text-center">
              <div class="mb-2">
                {{ t('Config.AP.TotalDistribution') }} {{ totalNum }}
              </div>
              <div>{{ t('Config.AP.Success') }} {{ successNum }} / {{ t('Config.AP.Failed') }} {{ failNum }}</div>
            </div>
          </div>
        </VCardText>
        <VCardActions class="d-flex justify-center">
          <VBtn
            v-if="distributeState === 2"
            color="primary"
            variant="flat"
            @click="distributeSuccess"
          >
            {{ t('Config.AP.Complete') }}
          </VBtn>
          <VBtn
            v-if="distributeState === 2"
            color="info"
            variant="tonal"
            class="ml-2"
            @click="exportLog"
          >
            {{ t('Config.AP.ExportLog') }}
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
    <!-- 👉 list -->
    <VCard class="mb-6">
      <div class="d-flex flex-wrap gap-4 ma-6">
        <BtnGroupSelector
          v-model:value="dType"
          :options="dTypeOptions"
        />
        <VSpacer />
        <div class="d-flex flex-wrap align-center gap-4">
          <!-- 新建配置模板按钮改为带菜单的按钮 -->
          <VMenu>
            <template #activator="{ props }">
              <VBtn
                color="success"
                prepend-icon="tabler-layout-grid-add"
                variant="tonal"
                v-bind="props"
              >
                {{ t('Config.AP.NewConfigTemplate') }}
              </VBtn>
            </template>
            <VList>
              <VListItem
                v-for="item in actionTypeList"
                :key="item.value"
                @click="() => { actionType = item.value; createNewTemplate(); }"
              >
                {{ item.label }}
              </VListItem>
            </VList>
          </VMenu>
          <VBtn
            color="primary"
            prepend-icon="tabler-file-export"
            @click="openDistributeTemplateDialog"
          >
            {{ t('Config.AP.SendTemplate') }}
          </VBtn>
        </div>
      </div>

      <VDivider class="mb-6" />
      <div class="d-flex flex-wrap gap-4 mx-6 justify-between">
        <AppSelect
          v-model="selectedModel"
          :items="modelList"
          item-title="label"
          item-value="value"
          :placeholder="t('Config.AP.PleaseSelectModel')"
          variant="outlined"
          density="compact"
          hide-details
          class="mr-4"
        />
        <AppTextField
          v-model="deviceName"
          :placeholder="t('Config.AP.SearchDevice')"
          variant="outlined"
          density="compact"
          hide-details
          class="mr-4"
        />
        <!--
          <div>
          <AppSelect
          v-model="itemsPerPage"
          :items="[5, 10, 20, 25, 50]"
          width="100"
          />
          </div>
        -->
      </div>
      <VDivider class="mt-6" />
      <VDataTableServer
        v-model="selectedRows"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="list"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
        class="text-no-wrap"
        item-value="sn"
        show-select
      >
        <template #item.type="{ item }">
          {{ item.type ? item.type : '--' }}
        </template>
        <template #item.onoff="{ item }">
          <VChip
            v-if="item.onoff === 'online'"
            label
            color="success"
            size="small"
          >
            {{ t('Config.AP.Online') }}
          </VChip>
          <VChip
            v-else
            label
            color="error"
            size="small"
          >
            {{ t('Config.AP.Offline') }}
          </VChip>
        </template>
        <!-- pagination -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalProduct"
          />
        </template>
      </VDataTableServer>
    </VCard>

    <!-- AC模板新建对话框 -->
    <VNavigationDrawer
      v-if="createACTemplateDialog"
      v-model="createACTemplateDialog"
      persistent
      location="right"
      temporary
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('Config.AP.NewACTemplate') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="createACTemplateDialog = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />
        <!-- 标签页固定 -->
        <div class="flex-shrink-0 px-6 pt-4">
          <VTabs
            v-model="currentACTab"
            class="mb-4"
            align-tabs="center"
            fixed-tabs
          >
            <VTab
              v-for="(item, index) in acTabList"
              :key="index"
              :value="item.value"
            >
              {{ item.label }}
            </VTab>
          </VTabs>
        </div>
        <!-- 内容区可滚动 -->
        <div class="flex-grow-1 px-6 pb-4 overflow-y-auto hide-scrollbar">
          <VWindow v-model="currentACTab">
            <!-- 基础设置 -->
            <VWindowItem :value="0">
              <VForm ref="acFormRef1">
                <!-- 模板名称 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('Config.Mode.TemplateName') }}
                  </div>
                  <AppTextField
                    v-model="acTemplateForm.name"
                    :rules="[(v: string) => requiredValidator(v, t('Config.Mode.EnterTemplateName'))]"
                    :placeholder="t('Config.Mode.EnterTemplateName')"
                  />
                </div>

                <!-- AC型号 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('Config.AP.SelectACModel') }}
                  </div>
                  <AppSelect
                    v-model="acTemplateForm.model"
                    :items="acModelList"
                    item-title="label"
                    item-value="value"
                    :placeholder="t('Config.AP.SelectACModel')"
                  />
                </div>

                <!-- 备注 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('Config.Mode.Remark') }}
                  </div>
                  <AppTextField
                    v-model="acTemplateForm.remark"
                    :placeholder="t('Config.Mode.Remark')"
                  />
                </div>
              </VForm>
            </VWindowItem>

            <!-- 网络设置 -->
            <VWindowItem :value="1">
              <VForm ref="acFormRef2">
                <!-- 网络类型选择 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('Config.Mode.NetworkType') }}
                  </div>
                  <AppSelect
                    v-model="acTemplateForm.networkConfig.networkType"
                    :items="networkList"
                    item-title="label"
                    item-value="value"
                    :placeholder="t('Config.Mode.SelectNetworkType')"
                  />
                </div>

                <!-- MTU设置 - 所有模式都可填 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('Config.AP.MTU') }}
                  </div>
                  <AppTextField
                    v-model="acTemplateForm.networkConfig.mtu"
                    :placeholder="t('Config.AP.EnterMTU')"
                    type="number"
                  />
                </div>

                <!-- PPPoE 模式字段 -->
                <div v-if="acTemplateForm.networkConfig.networkType === 1">
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.InternetAccount') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanUsername"
                      :rules="[
                        (v: string) => requiredValidator(v, t('NetworkConfig.Network.InternetAccountRequired')),
                        (v: string) => validateAccountAndPassword(v) ? true : t('NetworkConfig.Network.InternetAccountInvalid'),
                      ]"
                      :placeholder="t('NetworkConfig.Network.EnterInternetAccount')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.InternetPassword') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanPassword"
                      :rules="[
                        (v: string) => requiredValidator(v, t('NetworkConfig.Network.InternetPasswordRequired')),
                        (v: string) => validateAccountAndPassword(v) ? true : t('NetworkConfig.Network.InternetPasswordInvalid'),
                      ]"
                      :placeholder="t('NetworkConfig.Network.EnterInternetPassword')"
                      type="password"
                    />
                  </div>
                </div>

                <!-- 静态IP 模式字段 -->
                <div v-if="acTemplateForm.networkConfig.networkType === 2">
                  <!-- IP地址 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.IPAddress') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanIpAddress"
                      :rules="[
                        (v: string) => requiredValidator(v, t('NetworkConfig.Network.IPAddressRequired')),
                        (v: string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.IPAddressInvalid'),
                      ]"
                      :placeholder="t('NetworkConfig.Network.EnterIPAddress')"
                    />
                  </div>
                  <!-- 网关 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.Gateway') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanGateway"
                      :rules="[
                        (v: string) => requiredValidator(v, t('NetworkConfig.Network.GatewayRequired')),
                        (v: string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.GatewayInvalid'),
                      ]"
                      :placeholder="t('NetworkConfig.Network.EnterGateway')"
                    />
                  </div>
                  <!-- 子网掩码 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.Subnet') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanNetmask"
                      :rules="[
                        (v: string) => requiredValidator(v, t('NetworkConfig.Network.SubnetRequired')),
                        (v: string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.SubnetInvalid'),
                      ]"
                      :placeholder="t('NetworkConfig.Network.EnterSubnet')"
                    />
                  </div>

                  <!-- DNS服务器设置 -->
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.PrimaryDNS') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanDns[0]"
                      :placeholder="t('NetworkConfig.Network.EnterPrimaryDNS')"
                    />
                  </div>
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('NetworkConfig.Network.SecondaryDNS') }}
                    </div>
                    <AppTextField
                      v-model="acTemplateForm.networkConfig.wanDns[1]"
                      :placeholder="t('NetworkConfig.Network.EnterSecondaryDNS')"
                    />
                  </div>
                </div>
              </VForm>
            </VWindowItem>

            <!-- LAN设置 -->
            <VWindowItem :value="2">
              <VForm ref="acFormRef3">
                <!-- VLAN配置开关 -->
                <div class="d-flex justify-space-between align-center mb-4">
                  <div>
                    <div class="text-subtitle-2 text-on-surface opacity-90 mb-2">
                      {{ t('Config.Mode.ManagerVLAN') }}
                    </div>
                  </div>
                  <VSwitch
                    v-model="acTemplateForm.lanConfig.vlanEnabled"
                    true-value="1"
                    false-value="0"
                  />
                </div>

                <!-- VLAN配置（仅在启用时显示） -->
                <div v-if="acTemplateForm.lanConfig.vlanEnabled === '1'">
                  <div class="d-flex justify-space-between align-start mb-4">
                    <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                      {{ t('Config.Mode.VLANConfig') }}
                    </div>
                    <AppSelect
                      v-model="acTemplateForm.lanConfig.vlanTemplateId"
                      :items="vlanList"
                      :rules="[vlanValidator]"
                      item-title="itemTitle"
                      item-value="id"
                      :placeholder="t('Config.Mode.SelectVLAN')"
                    />
                  </div>
                </div>
                <!-- LAN IP地址 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('NetworkConfig.LAN.GatewayIP') }}
                  </div>
                  <AppTextField
                    v-model="acTemplateForm.lanConfig.lanIpAddress"
                    :rules="[lanIpValidator]"
                    :placeholder="t('NetworkConfig.LAN.EnterGatewayIP')"
                  />
                </div>

                <!-- 子网掩码 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('NetworkConfig.LAN.SubnetMask') }}
                  </div>
                  <AppSelect
                    v-model="acTemplateForm.lanConfig.lanNetmask"
                    :items="subnetMaskOptions"
                    :rules="[subnetMaskValidator]"
                    item-title="label"
                    item-value="value"
                    :placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
                  />
                </div>

                <!-- DHCP服务开关 -->
                <div class="d-flex justify-space-between align-center mb-4">
                  <div>
                    <div class="text-subtitle-2 text-on-surface opacity-90 mb-2">
                      {{ t('NetworkConfig.LAN.DHCPService') }}
                    </div>
                    <div class="text-subtitle-2 text-on-surface opacity-50">
                      {{ t('NetworkConfig.LAN.EnableDHCPDesc') }}
                    </div>
                  </div>
                  <VSwitch
                    v-model="acTemplateForm.lanConfig.dhcpDisabled"
                    true-value="0"
                    false-value="1"
                  />
                </div>

                <!-- DHCP配置（仅在启用时显示） -->
                <div v-if="acTemplateForm.lanConfig.dhcpDisabled === '0'">
                  <VCard
                    class="mt-4"
                    variant="outlined"
                  >
                    <VCardText>
                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('NetworkConfig.LAN.StartValue') }}
                        </div>
                        <div class="flex-grow-1">
                          <AppTextField
                            v-model="acTemplateForm.lanConfig.dhcpStartValue"
                            :rules="[dhcpValidator]"
                            :placeholder="t('NetworkConfig.LAN.EnterStartValue')"
                            type="number"
                            step="1"
                            min="1"
                            @keydown="preventDecimal"
                            @input="e => handleIntegerInput(e, 'dhcpStartValue')"
                          />
                          <div class="text-subtitle-2 text-on-surface opacity-50 mt-1">
                            {{ t('NetworkConfig.LAN.StartIPDesc') }}
                          </div>
                        </div>
                      </div>

                      <div class="d-flex justify-space-between align-start mb-4">
                        <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                          {{ t('NetworkConfig.LAN.MaxCount') }}
                        </div>
                        <AppTextField
                          v-model="acTemplateForm.lanConfig.dhcpMaxNumber"
                          :rules="[dhcpMaxValidator]"
                          :placeholder="t('NetworkConfig.LAN.EnterMaxCount')"
                          type="number"
                          step="1"
                          min="1"
                          @keydown="preventDecimal"
                          @input="e => handleIntegerInput(e, 'dhcpMaxNumber')"
                        />
                      </div>
                    </VCardText>
                  </VCard>
                </div>
              </VForm>
            </VWindowItem>

            <!-- 系统配置 -->
            <VWindowItem :value="3">
              <VForm ref="acFormRef4">
                <!-- 当前时间 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="flex-grow-1">
                    <VCard
                      variant="outlined"
                      class="pa-3"
                      style="background-color: #f5f5f5;"
                    >
                      <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                        {{ t('SystemConfig.System.CurrentTime') }}
                      </div>
                      <div class="text-success text-h6">
                        {{ currentTime }} {{ currentTimezone }}
                      </div>
                    </VCard>
                  </div>
                </div>

                <!-- 管理员密码 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('SystemConfig.System.AdminPassword') }}
                  </div>
                  <AppTextField
                    v-model="acTemplateForm.systemConfig.password"
                    :rules="[(v: string) => requiredValidator(v, t('SystemConfig.System.EnterAdminPassword'))]"
                    :placeholder="t('SystemConfig.System.EnterAdminPassword')"
                    :append-inner-icon="showAdminPassword ? 'tabler-eye-off' : 'tabler-eye'"
                    :type="showAdminPassword ? 'text' : 'password'"
                    @click:append-inner="showAdminPassword = !showAdminPassword"
                  />
                </div>

                <!-- 时区 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('SystemConfig.System.Timezone') }}
                  </div>
                  <AppSelect
                    v-model="acTemplateForm.systemConfig.timeZone"
                    :items="timezoneList"
                    :rules="[(v: string) => requiredValidator(v, t('SystemConfig.System.SelectTimezone'))]"
                    item-title="label"
                    :placeholder="t('SystemConfig.System.SelectTimezone')"
                  />
                </div>

                <!-- 设备名称 -->
                <div class="d-flex justify-space-between align-start mb-4">
                  <div class="text-subtitle-2 text-on-surface opacity-90 w-80px line-height-38px mr-2">
                    {{ t('SystemConfig.System.DeviceName') }}
                  </div>
                  <AppTextField
                    v-model="acTemplateForm.systemConfig.hostName"
                    :rules="[(v: string) => requiredValidator(v, t('SystemConfig.System.EnterDeviceName'))]"
                    :placeholder="t('SystemConfig.System.EnterDeviceName')"
                  />
                </div>
              </VForm>
            </VWindowItem>
          </VWindow>
        </div>

        <!-- 底部固定 -->
        <div class="flex-shrink-0 pa-4">
          <VDivider class="mb-4" />
          <div class="d-flex justify-end gap-4">
            <VBtn
              color="secondary"
              variant="tonal"
              @click="createACTemplateDialog = false"
            >
              {{ t('Config.Mode.Cancel') }}
            </VBtn>
            <VBtn
              v-if="currentACTab < 3"
              color="primary"
              @click="nextStepAC"
            >
              {{ t('Config.Mode.Next') }}
            </VBtn>
            <VBtn
              v-else
              color="primary"
              @click="saveACTemplate"
            >
              {{ t('Config.Mode.SaveTemplate') }}
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style lang="scss" scoped>
.table-title {
  color: text-primary;

  /* Basic Typography/h6 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 22px; /* 146.667% */
}

.table-name {
  color: text-primary;

  /* Components Typography/chip */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px; /* 153.846% */
}

.table-sn {
  color: text-secondary;

  /* Basic Typography/body-2 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 153.846% */
}

.table-item {
  display: flex;
  align-items: center;
  align-self: stretch;
  padding: 12px;
  border-radius: border-radius-md;
  background: grey-light;
  gap: 8px;
}

.stepper-line {
  border-width: 3px;
  border-color: rgba(var(--v-theme-primary));
  border-radius: 3px;

  &.active {
    opacity: 1;
  }
}

.stepper-circle {
  border-radius: 50%;
  background-color: #fff;
  block-size: 14px;
  inline-size: 14px;

  &.active {
    block-size: 10px;
    inline-size: 10px;
  }
}

.go-to-create {
  color: rgb(var(--v-theme-primary));
  cursor: pointer;
  font-size: 13px;
}

.progress {
  inline-size: 328px;
}

.distribute-log {
  color: rgba(47, 43, 61, 55%);
  font-size: 13px;
  text-align: center;
  user-select: none;
}

.w-80px {
  inline-size: 80px;
}

.line-height-38px {
  line-height: 38px;
}

.flexBox {
  display: flex;
  align-items: center;
}

.associated-device {
  &:hover {
    text-decoration: underline;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
